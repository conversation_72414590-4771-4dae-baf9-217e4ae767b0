using System;
using System.Threading.Tasks;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using Xunit;

namespace EmailProcessor.Infrastructure.Tests.Storage
{
    public class DualStorageServiceTests
    {
        private readonly ILoggingProvider _loggingProvider;
        private readonly LocalStorageProvider _localStorageProvider;
        private readonly NullStorageProvider _nullUncProvider;

        public DualStorageServiceTests()
        {
            _loggingProvider = new TestLoggingProvider();
            _localStorageProvider = new LocalStorageProvider(@"C:\Temp\EmailAttachments", _loggingProvider);
            _nullUncProvider = new NullStorageProvider(_loggingProvider);
        }

        [Fact]
        public void Constructor_WithNullStorageProvider_DoesNotThrow()
        {
            // Arrange & Act & Assert
            var dualStorageService = new DualStorageService(_localStorageProvider, _nullUncProvider, _loggingProvider);
            
            Assert.NotNull(dualStorageService);
        }

        [Fact]
        public async Task TestConnectivityAsync_WithNullUncProvider_ReturnsLocalConnectivityOnly()
        {
            // Arrange
            var dualStorageService = new DualStorageService(_localStorageProvider, _nullUncProvider, _loggingProvider);

            // Act
            var result = await dualStorageService.TestConnectivityAsync();

            // Assert
            // Should return true if local storage is available, regardless of UNC
            Assert.True(result);
        }

        [Fact]
        public async Task GetStorageHealthStatusAsync_WithNullUncProvider_ShowsUncAsUnavailable()
        {
            // Arrange
            var dualStorageService = new DualStorageService(_localStorageProvider, _nullUncProvider, _loggingProvider);

            // Act
            var healthStatus = await dualStorageService.GetStorageHealthStatusAsync();

            // Assert
            Assert.True(healthStatus.LocalStorageAvailable);
            Assert.False(healthStatus.UncStorageAvailable);
            Assert.True(healthStatus.LocalAvailableSpace > 0);
            Assert.Equal(0, healthStatus.UncAvailableSpace);
        }

        [Fact]
        public async Task SaveFileAsync_WithNullUncProvider_LocalSucceedsUncFails()
        {
            // Arrange
            var dualStorageService = new DualStorageService(_localStorageProvider, _nullUncProvider, _loggingProvider);
            var fileData = new byte[] { 1, 2, 3, 4, 5 };

            // Act
            var result = await dualStorageService.SaveFileAsync("test.txt", fileData, "test-dir");

            // Assert
            Assert.True(result.IsSuccessful); // Overall success because local succeeded
            Assert.True(result.LocalStorageResult.IsSuccessful);
            Assert.False(result.UncStorageResult.IsSuccessful);
            Assert.Contains("UNC storage is not available", result.UncStorageResult.ErrorMessage);
        }

        [Fact]
        public void Constructor_WithInvalidLocalProvider_ThrowsArgumentException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                new DualStorageService(_nullUncProvider, _nullUncProvider, _loggingProvider));
            
            Assert.Contains("First storage provider must be Local type", exception.Message);
        }

        [Fact]
        public void Constructor_WithInvalidUncProvider_ThrowsArgumentException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                new DualStorageService(_localStorageProvider, _localStorageProvider, _loggingProvider));
            
            Assert.Contains("Second storage provider must be UNC type", exception.Message);
        }

        private class TestLoggingProvider : ILoggingProvider
        {
            public LogLevel CurrentLogLevel => LogLevel.Information;

            public Task LogAsync(LogLevel logLevel, string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogDebugAsync(string message, string sourceComponent, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogErrorAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogFatalAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogInformationAsync(string message, string sourceComponent, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogWarningAsync(string message, string sourceComponent, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public void SetLogLevel(LogLevel logLevel) { }

            public bool IsEnabled(LogLevel logLevel) => true;
        }
    }
}