using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.IO; // Added for Path.GetInvalidPathChars()

namespace EmailProcessor.Shared.Utilities
{
    /// <summary>
    /// Utility for normalizing names for use in directory structures
    /// </summary>
    public static class NameNormalizationUtility
    {
        private const int MaxNameLength = 50;
        private const string UnknownSender = "Unknown_Sender";
        private const string UnknownRecipient = "Unknown_Recipient";
        private const string UnknownUnknown = "Unknown_Unknown";

        /// <summary>
        /// Normalizes a name for use in directory structures
        /// </summary>
        /// <param name="name">The name to normalize</param>
        /// <param name="fallbackName">Fallback name if normalization fails</param>
        /// <returns>Normalized name safe for filesystem use</returns>
        public static string NormalizeName(string name, string fallbackName = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                return fallbackName ?? UnknownUnknown;

            try
            {
                // Extract name from email format: "<PERSON> <<EMAIL>>"
                var cleanName = ExtractNameFromEmail(name);
                
                // Remove special characters and normalize
                var normalized = NormalizeSpecialCharacters(cleanName);
                
                // Truncate if too long
                normalized = TruncateName(normalized);
                
                // Ensure it's not empty after normalization
                if (string.IsNullOrWhiteSpace(normalized))
                    return fallbackName ?? UnknownUnknown;

                return normalized;
            }
            catch
            {
                return fallbackName ?? UnknownUnknown;
            }
        }

        /// <summary>
        /// Extracts display name from email format
        /// </summary>
        /// <param name="emailString">Email string in format "Name <<EMAIL>>"</param>
        /// <returns>Extracted name or original string</returns>
        public static string ExtractNameFromEmail(string emailString)
        {
            if (string.IsNullOrWhiteSpace(emailString))
                return emailString;

            // Pattern: "Name <<EMAIL>>" or "<EMAIL>"
            var match = Regex.Match(emailString, @"^(.+?)\s*<(.+?)>$");
            
            if (match.Success)
            {
                var name = match.Groups[1].Value.Trim();
                return !string.IsNullOrWhiteSpace(name) ? name : match.Groups[2].Value;
            }

            // If no angle brackets, return as is
            return emailString;
        }

        /// <summary>
        /// Normalizes special characters for filesystem compatibility
        /// </summary>
        /// <param name="name">Name to normalize</param>
        /// <returns>Normalized name</returns>
        public static string NormalizeSpecialCharacters(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return name;

            // Replace invalid filesystem characters
            var invalidChars = new[] { '<', '>', ':', '"', '|', '?', '*', '\\', '/', '\t', '\n', '\r' };
            var result = name;

            foreach (var invalidChar in invalidChars)
            {
                result = result.Replace(invalidChar, '_');
            }

            // Replace multiple spaces with single underscore
            result = Regex.Replace(result, @"\s+", "_");
            
            // Replace multiple underscores with single underscore
            result = Regex.Replace(result, @"_+", "_");
            
            // Remove leading/trailing underscores
            result = result.Trim('_');

            return result;
        }

        /// <summary>
        /// Truncates name to maximum length
        /// </summary>
        /// <param name="name">Name to truncate</param>
        /// <returns>Truncated name</returns>
        public static string TruncateName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return name;

            if (name.Length <= MaxNameLength)
                return name;

            // Try to truncate at word boundary
            var truncated = name.Substring(0, MaxNameLength);
            var lastUnderscore = truncated.LastIndexOf('_');
            
            if (lastUnderscore > MaxNameLength * 0.7) // If underscore is in last 30%
            {
                truncated = truncated.Substring(0, lastUnderscore);
            }

            return truncated.TrimEnd('_');
        }

        /// <summary>
        /// Normalizes sender name with fallback
        /// </summary>
        /// <param name="senderName">Sender name or email</param>
        /// <returns>Normalized sender name</returns>
        public static string NormalizeSenderName(string senderName)
        {
            return NormalizeName(senderName, UnknownSender);
        }

        /// <summary>
        /// Normalizes recipient name with fallback
        /// </summary>
        /// <param name="recipientName">Recipient name or email</param>
        /// <returns>Normalized recipient name</returns>
        public static string NormalizeRecipientName(string recipientName)
        {
            return NormalizeName(recipientName, UnknownRecipient);
        }

        /// <summary>
        /// Processes multiple recipients for sent emails
        /// </summary>
        /// <param name="recipients">List of recipient names</param>
        /// <returns>List of normalized recipient names</returns>
        public static List<string> NormalizeRecipients(List<string> recipients)
        {
            if (recipients == null || !recipients.Any())
                return new List<string> { UnknownRecipient };

            var normalized = new List<string>();
            
            foreach (var recipient in recipients)
            {
                var normalizedName = NormalizeRecipientName(recipient);
                if (!normalized.Contains(normalizedName))
                {
                    normalized.Add(normalizedName);
                }
            }

            return normalized;
        }

        /// <summary>
        /// Validates if a normalized name is valid for filesystem use
        /// </summary>
        /// <param name="normalizedName">Normalized name to validate</param>
        /// <returns>True if valid</returns>
        public static bool IsValidDirectoryName(string normalizedName)
        {
            if (string.IsNullOrWhiteSpace(normalizedName))
                return false;

            // Check for reserved names
            var reservedNames = new[] { "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9" };
            
            if (reservedNames.Contains(normalizedName.ToUpper()))
                return false;

            // Check for invalid characters
            var invalidChars = Path.GetInvalidPathChars();
            if (normalizedName.Any(c => invalidChars.Contains(c)))
                return false;

            return true;
        }
    }
} 