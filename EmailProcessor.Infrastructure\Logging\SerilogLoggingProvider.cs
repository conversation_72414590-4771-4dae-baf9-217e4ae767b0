using System;
using System.IO;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using Serilog;
using Serilog.Events;
using Serilog.Core;
using Serilog.Formatting.Json;

namespace EmailProcessor.Infrastructure.Logging
{
    /// <summary>
    /// Serilog-based implementation of ILoggingProvider with structured logging and correlation ID support
    /// </summary>
    public class SerilogLoggingProvider : ILoggingProvider, IDisposable
    {
        private readonly ILogger _logger;
        private readonly LoggingLevelSwitch _levelSwitch;
        private LogLevel _currentLogLevel;

        public SerilogLoggingProvider(SerilogLoggingConfiguration config = null)
        {
            config ??= new SerilogLoggingConfiguration();
            
            _levelSwitch = new LoggingLevelSwitch(ConvertToSerilogLevel(config.MinimumLevel));
            _currentLogLevel = config.MinimumLevel;

            var loggerConfig = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(_levelSwitch)
                .Enrich.WithProperty("Application", "EmailProcessor")
                .Enrich.WithProperty("MachineName", Environment.MachineName)
                .Enrich.WithProperty("ProcessId", Environment.ProcessId);

            // Configure console sink if enabled
            if (config.EnableConsoleLogging)
            {
                if (config.UseStructuredLogging)
                {
                    loggerConfig.WriteTo.Console(new JsonFormatter());
                }
                else
                {
                    loggerConfig.WriteTo.Console(
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceComponent}] {Message:lj} {CorrelationId} {NewLine}{Exception}"
                    );
                }
            }

            // Configure file sink if enabled
            if (config.EnableFileLogging)
            {
                var logDirectory = Path.GetDirectoryName(config.LogFilePath);
                if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                if (config.UseStructuredLogging)
                {
                    loggerConfig.WriteTo.File(
                        new JsonFormatter(),
                        config.LogFilePath,
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: config.RetainedFileCount,
                        fileSizeLimitBytes: config.FileSizeLimitBytes,
                        shared: true,
                        flushToDiskInterval: TimeSpan.FromSeconds(config.FlushIntervalSeconds)
                    );
                }
                else
                {
                    loggerConfig.WriteTo.File(
                        config.LogFilePath,
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: config.RetainedFileCount,
                        fileSizeLimitBytes: config.FileSizeLimitBytes,
                        shared: true,
                        flushToDiskInterval: TimeSpan.FromSeconds(config.FlushIntervalSeconds),
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceComponent}] {Message:lj} {CorrelationId} {NewLine}{Exception}"
                    );
                }
            }

            _logger = loggerConfig.CreateLogger();
            
            // Log initialization
            _logger.Information("SerilogLoggingProvider initialized with configuration: MinLevel={MinLevel}, Console={Console}, File={File}, Structured={Structured}", 
                config.MinimumLevel, config.EnableConsoleLogging, config.EnableFileLogging, config.UseStructuredLogging);
        }

        public LogLevel CurrentLogLevel => _currentLogLevel;

        public void SetLogLevel(LogLevel logLevel)
        {
            _currentLogLevel = logLevel;
            _levelSwitch.MinimumLevel = ConvertToSerilogLevel(logLevel);
            
            _logger.Information("Log level changed to {LogLevel}", logLevel);
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return _logger.IsEnabled(ConvertToSerilogLevel(logLevel));
        }

        public async Task LogAsync(LogLevel logLevel, string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
        {
            if (!IsEnabled(logLevel))
                return;

            var serilogLevel = ConvertToSerilogLevel(logLevel);
            var correlationIdProperty = correlationId?.ToString() ?? "N/A";

            if (exception != null)
            {
                _logger
                    .ForContext("SourceComponent", sourceComponent ?? "Unknown")
                    .ForContext("CorrelationId", correlationIdProperty)
                    .Write(serilogLevel, exception, message);
            }
            else
            {
                _logger
                    .ForContext("SourceComponent", sourceComponent ?? "Unknown")
                    .ForContext("CorrelationId", correlationIdProperty)
                    .Write(serilogLevel, message);
            }

            await Task.CompletedTask;
        }

        public async Task LogDebugAsync(string message, string sourceComponent, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Debug, message, sourceComponent, null, correlationId);
        }

        public async Task LogInformationAsync(string message, string sourceComponent, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Information, message, sourceComponent, null, correlationId);
        }

        public async Task LogWarningAsync(string message, string sourceComponent, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Warning, message, sourceComponent, null, correlationId);
        }

        public async Task LogErrorAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Error, message, sourceComponent, exception, correlationId);
        }

        public async Task LogFatalAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Fatal, message, sourceComponent, exception, correlationId);
        }

        /// <summary>
        /// Converts domain LogLevel to Serilog LogEventLevel
        /// </summary>
        private static LogEventLevel ConvertToSerilogLevel(LogLevel logLevel)
        {
            return logLevel switch
            {
                LogLevel.Debug => LogEventLevel.Debug,
                LogLevel.Information => LogEventLevel.Information,
                LogLevel.Warning => LogEventLevel.Warning,
                LogLevel.Error => LogEventLevel.Error,
                LogLevel.Fatal => LogEventLevel.Fatal,
                _ => LogEventLevel.Information
            };
        }

        public void Dispose()
        {
            _logger?.Dispose();
        }
    }

    /// <summary>
    /// Configuration class for SerilogLoggingProvider
    /// </summary>
    public class SerilogLoggingConfiguration
    {
        /// <summary>
        /// Minimum log level to record
        /// </summary>
        public LogLevel MinimumLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// Enable console logging
        /// </summary>
        public bool EnableConsoleLogging { get; set; } = true;

        /// <summary>
        /// Enable file logging
        /// </summary>
        public bool EnableFileLogging { get; set; } = true;

        /// <summary>
        /// Log file path (will create directory if needed)
        /// </summary>
        public string LogFilePath { get; set; } = Path.Combine("Logs", "EmailProcessor-.log");

        /// <summary>
        /// Use structured JSON logging format
        /// </summary>
        public bool UseStructuredLogging { get; set; } = false;

        /// <summary>
        /// Number of log files to retain
        /// </summary>
        public int RetainedFileCount { get; set; } = 30;

        /// <summary>
        /// Maximum size of each log file in bytes (default: 100MB)
        /// </summary>
        public long FileSizeLimitBytes { get; set; } = 100 * 1024 * 1024;

        /// <summary>
        /// Interval in seconds to flush logs to disk
        /// </summary>
        public int FlushIntervalSeconds { get; set; } = 5;
    }
}