using System;
using System.Collections.Generic;
using System.Linq;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Configuration;
using EmailProcessor.Shared.Models;
using EmailProcessor.Shared.Utilities;

namespace EmailProcessor.Infrastructure.Services
{
    /// <summary>
    /// Service for creating enhanced directory structures with sender and recipient information
    /// </summary>
    public class EnhancedDirectoryStructureService
    {
        private readonly StorageConfiguration _storageConfiguration;

        public EnhancedDirectoryStructureService(StorageConfiguration storageConfiguration)
        {
            _storageConfiguration = storageConfiguration ?? throw new ArgumentNullException(nameof(storageConfiguration));
        }

        /// <summary>
        /// Creates directory structure based on configuration mode
        /// </summary>
        /// <param name="emailType">Type of email (Sent/Received)</param>
        /// <param name="timestamp">Email timestamp</param>
        /// <param name="emailData">Email data containing sender and recipient information</param>
        /// <returns>Directory structure path</returns>
        public string CreateDirectoryStructure(EmailType emailType, DateTime timestamp, EmailData emailData)
        {
            if (_storageConfiguration.DirectoryStructureMode == DirectoryStructureMode.Legacy)
            {
                return CreateLegacyDirectoryStructure(emailType, timestamp);
            }

            return CreateEnhancedDirectoryStructure(emailType, timestamp, emailData);
        }

        /// <summary>
        /// Creates legacy directory structure
        /// </summary>
        /// <param name="emailType">Type of email</param>
        /// <param name="timestamp">Email timestamp</param>
        /// <returns>Legacy directory structure</returns>
        public string CreateLegacyDirectoryStructure(EmailType emailType, DateTime timestamp)
        {
            return $"{timestamp.Year}\\{timestamp.Month:D2}\\{timestamp.Day:D2}\\{emailType}";
        }

        /// <summary>
        /// Creates enhanced directory structure with sender and recipient information
        /// </summary>
        /// <param name="emailType">Type of email</param>
        /// <param name="timestamp">Email timestamp</param>
        /// <param name="emailData">Email data</param>
        /// <returns>Enhanced directory structure</returns>
        public string CreateEnhancedDirectoryStructure(EmailType emailType, DateTime timestamp, EmailData emailData)
        {
            var baseStructure = $"{timestamp.Year}\\{timestamp.Month:D2}\\{timestamp.Day:D2}\\{emailType}";

            if (emailData == null)
                return baseStructure;

            var senderName = NormalizeSenderName(emailData);
            var recipientNames = GetRecipientNames(emailData, emailType);

            if (recipientNames.Count == 1)
            {
                // Single recipient
                return $"{baseStructure}\\{senderName}\\{recipientNames[0]}";
            }
            else if (recipientNames.Count > 1)
            {
                // Multiple recipients - handle based on configuration
                return HandleMultipleRecipients(baseStructure, senderName, recipientNames);
            }
            else
            {
                // No recipients found
                return $"{baseStructure}\\{senderName}\\{NameNormalizationUtility.UnknownRecipient}";
            }
        }

        /// <summary>
        /// Creates directory structures for multiple recipients
        /// </summary>
        /// <param name="emailType">Type of email</param>
        /// <param name="timestamp">Email timestamp</param>
        /// <param name="emailData">Email data</param>
        /// <returns>List of directory structures for each recipient</returns>
        public List<string> CreateDirectoryStructuresForMultipleRecipients(EmailType emailType, DateTime timestamp, EmailData emailData)
        {
            if (_storageConfiguration.DirectoryStructureMode == DirectoryStructureMode.Legacy)
            {
                return new List<string> { CreateLegacyDirectoryStructure(emailType, timestamp) };
            }

            var baseStructure = $"{timestamp.Year}\\{timestamp.Month:D2}\\{timestamp.Day:D2}\\{emailType}";
            var senderName = NormalizeSenderName(emailData);
            var recipientNames = GetRecipientNames(emailData, emailType);

            if (recipientNames.Count <= 1)
            {
                return new List<string> { CreateEnhancedDirectoryStructure(emailType, timestamp, emailData) };
            }

            // Create separate structure for each recipient
            var structures = new List<string>();
            foreach (var recipientName in recipientNames)
            {
                structures.Add($"{baseStructure}\\{senderName}\\{recipientName}");
            }

            return structures;
        }

        /// <summary>
        /// Normalizes sender name from email data
        /// </summary>
        /// <param name="emailData">Email data</param>
        /// <returns>Normalized sender name</returns>
        private string NormalizeSenderName(EmailData emailData)
        {
            if (emailData == null)
                return NameNormalizationUtility.UnknownSender;

            var senderName = !string.IsNullOrWhiteSpace(emailData.SenderName) 
                ? emailData.SenderName 
                : emailData.SenderEmail;

            return NameNormalizationUtility.NormalizeSenderName(senderName);
        }

        /// <summary>
        /// Gets recipient names based on email type and configuration
        /// </summary>
        /// <param name="emailData">Email data</param>
        /// <param name="emailType">Email type</param>
        /// <returns>List of normalized recipient names</returns>
        private List<string> GetRecipientNames(EmailData emailData, EmailType emailType)
        {
            if (emailData == null)
                return new List<string> { NameNormalizationUtility.UnknownRecipient };

            var recipients = new List<string>();

            // Add primary recipients (To field)
            if (emailData.RecipientTo != null && emailData.RecipientTo.Any())
            {
                recipients.AddRange(emailData.RecipientTo);
            }

            // Add CC recipients if configured
            if (_storageConfiguration.IncludeCcRecipients && emailData.RecipientCC != null && emailData.RecipientCC.Any())
            {
                recipients.AddRange(emailData.RecipientCC);
            }

            // Add BCC recipients if configured
            if (_storageConfiguration.IncludeBccRecipients && emailData.RecipientBCC != null && emailData.RecipientBCC.Any())
            {
                recipients.AddRange(emailData.RecipientBCC);
            }

            // Normalize all recipient names
            var normalizedRecipients = NameNormalizationUtility.NormalizeRecipients(recipients);

            // Remove duplicates
            return normalizedRecipients.Distinct().ToList();
        }

        /// <summary>
        /// Handles multiple recipients based on configuration
        /// </summary>
        /// <param name="baseStructure">Base directory structure</param>
        /// <param name="senderName">Normalized sender name</param>
        /// <param name="recipientNames">List of recipient names</param>
        /// <returns>Directory structure string</returns>
        private string HandleMultipleRecipients(string baseStructure, string senderName, List<string> recipientNames)
        {
            switch (_storageConfiguration.MultipleRecipientHandling)
            {
                case MultipleRecipientHandling.SeparateFolders:
                    // Return structure for primary recipient (first in list)
                    return $"{baseStructure}\\{senderName}\\{recipientNames[0]}";

                case MultipleRecipientHandling.CombinedName:
                    // Use combined name for multiple recipients
                    var combinedName = recipientNames.Count <= 3 
                        ? string.Join("_", recipientNames)
                        : $"Multiple_Recipients_{recipientNames.Count}";
                    return $"{baseStructure}\\{senderName}\\{combinedName}";

                case MultipleRecipientHandling.PrimaryOnly:
                    // Use only primary recipient
                    return $"{baseStructure}\\{senderName}\\{recipientNames[0]}";

                default:
                    return $"{baseStructure}\\{senderName}\\{recipientNames[0]}";
            }
        }

        /// <summary>
        /// Validates directory structure for filesystem compatibility
        /// </summary>
        /// <param name="directoryStructure">Directory structure to validate</param>
        /// <returns>True if valid</returns>
        public bool ValidateDirectoryStructure(string directoryStructure)
        {
            if (string.IsNullOrWhiteSpace(directoryStructure))
                return false;

            var parts = directoryStructure.Split('\\');
            
            foreach (var part in parts)
            {
                if (!NameNormalizationUtility.IsValidDirectoryName(part))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Gets the maximum path length for the directory structure
        /// </summary>
        /// <param name="basePath">Base storage path</param>
        /// <param name="directoryStructure">Directory structure</param>
        /// <param name="fileName">File name</param>
        /// <returns>Total path length</returns>
        public int GetPathLength(string basePath, string directoryStructure, string fileName)
        {
            var fullPath = $"{basePath}\\{directoryStructure}\\{fileName}";
            return fullPath.Length;
        }

        /// <summary>
        /// Checks if path length exceeds Windows limit (260 characters)
        /// </summary>
        /// <param name="basePath">Base storage path</param>
        /// <param name="directoryStructure">Directory structure</param>
        /// <param name="fileName">File name</param>
        /// <returns>True if path is too long</returns>
        public bool IsPathTooLong(string basePath, string directoryStructure, string fileName)
        {
            return GetPathLength(basePath, directoryStructure, fileName) > 260;
        }
    }
} 