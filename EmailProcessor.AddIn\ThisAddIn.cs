﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Outlook = Microsoft.Office.Interop.Outlook;
using Office = Microsoft.Office.Core;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;
using EmailProcessor.AddIn.Services;
using EmailProcessor.AddIn.Communication;
using EmailProcessor.AddIn.Configuration;

namespace EmailProcessor.AddIn
{
    public partial class ThisAddIn
    {
        private IVSTOConfiguration _configuration;
        private IOutlookIntegration _outlookIntegration;
        private INamedPipeClient _namedPipeClient;
        private bool _isInitialized = false;
        private readonly object _processingLock = new object();

        private void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            try
            {
                InitializeAddIn();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during add-in startup: {ex.Message}");
                MessageBox.Show($"Email Processor Add-in failed to start: {ex.Message}", "Email Processor Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {
            try
            {
                CleanupAddIn();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during add-in shutdown: {ex.Message}");
            }
        }

        private void InitializeAddIn()
        {
            try
            {
                // Initialize configuration
                _configuration = new VSTOConfiguration();

                // Initialize services
                _outlookIntegration = new OutlookIntegrationService(_configuration);
                _namedPipeClient = new NamedPipeClient(_configuration.NamedPipeName);

                // Set up event handlers
                SetupOutlookEventHandlers();

                // Connect to Windows Service
                ConnectToWindowsService();

                _isInitialized = true;

                System.Diagnostics.Debug.WriteLine("Email Processor Add-in initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing add-in: {ex.Message}");
                throw;
            }
        }

        private void SetupOutlookEventHandlers()
        {
            try
            {
                // Hook up to Application events
                Application.NewMailEx += Application_NewMailEx;
                Application.ItemSend += Application_ItemSend;

                System.Diagnostics.Debug.WriteLine("Outlook event handlers set up successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting up Outlook event handlers: {ex.Message}");
                throw;
            }
        }

        private void ConnectToWindowsService()
        {
            try
            {
                // Attempt to connect to Windows Service
                var connectTask = _namedPipeClient.ConnectAsync(_configuration.ConnectionTimeoutSeconds);
                connectTask.Wait();

                if (connectTask.Result)
                {
                    System.Diagnostics.Debug.WriteLine("Connected to Email Processor Windows Service");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Failed to connect to Email Processor Windows Service");
                }

                // Set up connection event handlers
                _namedPipeClient.ConnectionLost += OnConnectionLost;
                _namedPipeClient.Connected += OnConnected;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error connecting to Windows Service: {ex.Message}");
            }
        }

        private void Application_NewMailEx(string EntryIDCollection)
        {
            try
            {
                if (!_isInitialized || !_configuration.ProcessReceivedEmails)
                    return;

                // Process received email
                ProcessEmail(EntryIDCollection, "Received");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error processing received email: {ex.Message}");
                // Don't show error to user for received emails to avoid disruption
            }
        }

        private void Application_ItemSend(object Item, ref bool Cancel)
        {
            try
            {
                if (!_isInitialized || !_configuration.ProcessSentEmails)
                    return;

                // Process sent email
                ProcessEmail(Item, "Sent");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error processing sent email: {ex.Message}");
                // Don't show error to user for sent emails to avoid disruption
            }
        }

        private void ProcessEmail(object emailItem, string emailType)
        {
            // Use lock to prevent concurrent processing of the same email
            lock (_processingLock)
            {
                try
                {
                    // Extract email data
                    var emailData = _outlookIntegration.ExtractEmailData(emailItem);

                    // Check if email should be processed
                    if (!_outlookIntegration.ShouldProcessEmail(emailItem))
                    {
                        System.Diagnostics.Debug.WriteLine($"Email skipped: {emailData.Subject}");
                        return;
                    }

                    // Extract attachment data
                    var attachments = _outlookIntegration.ExtractAttachmentData(emailItem, _configuration.MaxFileSizeBytes);

                    if (!attachments.Any())
                    {
                        System.Diagnostics.Debug.WriteLine($"No processable attachments found in email: {emailData.Subject}");
                        return;
                    }

                    // Create processing message
                    var processingMessage = new ProcessingMessage
                    {
                        Email = emailData,
                        Attachments = attachments
                    };

                    // Send to Windows Service asynchronously
                    Task.Run(async () => await SendToWindowsServiceAsync(processingMessage));

                    System.Diagnostics.Debug.WriteLine($"Email queued for processing: {emailData.Subject} with {attachments.Count} attachments");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error processing email: {ex.Message}");
                }
            }
        }

        private async Task SendToWindowsServiceAsync(ProcessingMessage message)
        {
            try
            {
                // Ensure connection to Windows Service
                if (!_namedPipeClient.IsConnected)
                {
                    var connected = await _namedPipeClient.ConnectAsync(_configuration.ConnectionTimeoutSeconds);
                    if (!connected)
                    {
                        System.Diagnostics.Debug.WriteLine("Failed to connect to Windows Service for email processing");
                        return;
                    }
                }

                // Send message with retry logic
                ProcessingResponse response = null;
                Exception lastException = null;

                for (int attempt = 1; attempt <= _configuration.RetryCount; attempt++)
                {
                    try
                    {
                        response = await _namedPipeClient.SendMessageAsync(message);
                        break; // Success, exit retry loop
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        System.Diagnostics.Debug.WriteLine($"Attempt {attempt} failed: {ex.Message}");

                        if (attempt < _configuration.RetryCount)
                        {
                            // Wait before retry
                            await Task.Delay(TimeSpan.FromSeconds(_configuration.RetryDelaySeconds));
                        }
                    }
                }

                if (response != null)
                {
                    if (response.Success)
                    {
                        System.Diagnostics.Debug.WriteLine($"Email processed successfully: {message.Email.Subject}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Email processing failed: {response.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Email processing failed after {_configuration.RetryCount} attempts: {(lastException != null ? lastException.Message : "Unknown error")}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error sending email to Windows Service: {ex.Message}");
            }
        }

        private void OnConnectionLost(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("Connection to Windows Service lost");
            
            // Attempt to reconnect
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(5)); // Wait before reconnection attempt
                    await _namedPipeClient.ConnectAsync(_configuration.ConnectionTimeoutSeconds);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to reconnect to Windows Service: {ex.Message}");
                }
            });
        }

        private void OnConnected(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("Reconnected to Windows Service");
        }

        private void CleanupAddIn()
        {
            try
            {
                // Remove event handlers
                if (Application != null)
                {
                    Application.NewMailEx -= Application_NewMailEx;
                    Application.ItemSend -= Application_ItemSend;
                }

                // Dispose Named Pipe client
                if (_namedPipeClient != null)
                {
                    _namedPipeClient.Dispose();
                }

                _isInitialized = false;

                System.Diagnostics.Debug.WriteLine("Email Processor Add-in cleaned up successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during add-in cleanup: {ex.Message}");
            }
        }

        #region VSTO generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InternalStartup()
        {
            this.Startup += new System.EventHandler(ThisAddIn_Startup);
            this.Shutdown += new System.EventHandler(ThisAddIn_Shutdown);
        }
        
        #endregion
    }
}
