using Microsoft.EntityFrameworkCore;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Infrastructure.Database.Context
{
    public class EmailProcessorContext : DbContext
    {
        public DbSet<Email> Emails { get; set; }
        public DbSet<Attachment> Attachments { get; set; }
        public DbSet<ProcessingLog> ProcessingLogs { get; set; }
        public DbSet<Configuration> Configuration { get; set; }

        public EmailProcessorContext(DbContextOptions<EmailProcessorContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Email entity configuration
            modelBuilder.Entity<Email>(entity =>
            {
                entity.ToTable("Emails");
                entity.HasKey(e => e.EmailId);
                entity.Property(e => e.EmailId).ValueGeneratedOnAdd();
                
                entity.Property(e => e.Subject).IsRequired().HasMaxLength(500);
                entity.Property(e => e.SenderName).IsRequired().HasMaxLength(200);
                
                // Configure value objects as owned types
                entity.OwnsOne(e => e.SenderEmail, emailAddress =>
                {
                    emailAddress.Property(ea => ea.Value).HasColumnName("SenderEmail").IsRequired().HasMaxLength(200);
                });
                
                entity.OwnsMany(e => e.RecipientTo, recipientTo =>
                {
                    recipientTo.Property(rt => rt.Value).HasColumnName("RecipientTo").IsRequired().HasMaxLength(2000);
                });
                
                entity.OwnsMany(e => e.RecipientCC, recipientCC =>
                {
                    recipientCC.Property(rc => rc.Value).HasColumnName("RecipientCC").HasMaxLength(2000);
                });
                
                entity.Property(e => e.EmailType).IsRequired().HasConversion<int>();
                entity.Property(e => e.Timestamp).IsRequired();
                entity.Property(e => e.OutlookMessageId).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ProcessingStatus).IsRequired().HasConversion<int>();
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.ModifiedDate).IsRequired();

                // Indexes
                entity.HasIndex(e => e.Timestamp).HasDatabaseName("IX_Emails_Timestamp");
                entity.HasIndex(e => e.EmailType).HasDatabaseName("IX_Emails_EmailType");
                entity.HasIndex(e => e.ProcessingStatus).HasDatabaseName("IX_Emails_ProcessingStatus");
                entity.HasIndex(e => e.OutlookMessageId).HasDatabaseName("IX_Emails_OutlookMessageId").IsUnique();
            });

            // Attachment entity configuration
            modelBuilder.Entity<Attachment>(entity =>
            {
                entity.ToTable("Attachments");
                entity.HasKey(e => e.AttachmentId);
                entity.Property(e => e.AttachmentId).ValueGeneratedOnAdd();
                
                entity.Property(e => e.EmailId).IsRequired();
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(500);
                entity.Property(e => e.OriginalFileName).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).IsRequired().HasMaxLength(200);
                entity.Property(e => e.FileExtension).IsRequired().HasMaxLength(50);
                entity.Property(e => e.FileSize).IsRequired();
                
                // Configure value objects as owned types
                entity.OwnsOne(e => e.LocalStoragePath, localPath =>
                {
                    localPath.Property(lp => lp.Value).HasColumnName("LocalStoragePath").IsRequired().HasMaxLength(1000);
                });
                
                entity.OwnsOne(e => e.UncStoragePath, uncPath =>
                {
                    uncPath.Property(up => up.Value).HasColumnName("UncStoragePath").IsRequired().HasMaxLength(1000);
                });
                
                entity.Property(e => e.LocalProcessingStatus).IsRequired().HasConversion<int>();
                entity.Property(e => e.UncProcessingStatus).IsRequired().HasConversion<int>();
                entity.Property(e => e.LocalErrorMessage).HasMaxLength(-1); // NVARCHAR(MAX)
                entity.Property(e => e.UncErrorMessage).HasMaxLength(-1); // NVARCHAR(MAX)
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.ModifiedDate).IsRequired();

                // Foreign key relationship
                entity.HasOne<Email>()
                    .WithMany(e => e.Attachments)
                    .HasForeignKey(a => a.EmailId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => e.EmailId).HasDatabaseName("IX_Attachments_EmailId");
                entity.HasIndex(e => e.LocalProcessingStatus).HasDatabaseName("IX_Attachments_LocalProcessingStatus");
                entity.HasIndex(e => e.UncProcessingStatus).HasDatabaseName("IX_Attachments_UncProcessingStatus");
                entity.HasIndex(e => e.FileExtension).HasDatabaseName("IX_Attachments_FileExtension");
            });

            // ProcessingLog entity configuration
            modelBuilder.Entity<ProcessingLog>(entity =>
            {
                entity.ToTable("ProcessingLogs");
                entity.HasKey(e => e.LogId);
                entity.Property(e => e.LogId).ValueGeneratedOnAdd();
                
                entity.Property(e => e.EmailId);
                entity.Property(e => e.AttachmentId);
                entity.Property(e => e.LogLevel).IsRequired().HasConversion<int>();
                entity.Property(e => e.Message).IsRequired().HasMaxLength(-1); // NVARCHAR(MAX)
                entity.Property(e => e.ExceptionDetails).HasMaxLength(-1); // NVARCHAR(MAX)
                entity.Property(e => e.SourceComponent).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CorrelationId);
                entity.Property(e => e.CreatedDate).IsRequired();

                // Foreign key relationships
                entity.HasOne<Email>()
                    .WithMany(e => e.ProcessingLogs)
                    .HasForeignKey(l => l.EmailId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne<Attachment>()
                    .WithMany()
                    .HasForeignKey(l => l.AttachmentId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => e.EmailId).HasDatabaseName("IX_ProcessingLogs_EmailId");
                entity.HasIndex(e => e.AttachmentId).HasDatabaseName("IX_ProcessingLogs_AttachmentId");
                entity.HasIndex(e => e.LogLevel).HasDatabaseName("IX_ProcessingLogs_LogLevel");
                entity.HasIndex(e => e.CreatedDate).HasDatabaseName("IX_ProcessingLogs_CreatedDate");
                entity.HasIndex(e => e.CorrelationId).HasDatabaseName("IX_ProcessingLogs_CorrelationId");
            });

            // Configuration entity configuration
            modelBuilder.Entity<Configuration>(entity =>
            {
                entity.ToTable("Configuration");
                entity.HasKey(e => e.ConfigId);
                entity.Property(e => e.ConfigId).ValueGeneratedOnAdd();
                
                entity.Property(e => e.ConfigKey).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ConfigValue).IsRequired().HasMaxLength(-1); // NVARCHAR(MAX)
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.IsEncrypted).IsRequired();
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.ModifiedDate).IsRequired();

                // Unique constraint on ConfigKey
                entity.HasIndex(e => e.ConfigKey).HasDatabaseName("IX_Configuration_ConfigKey").IsUnique();
            });
        }
    }
} 