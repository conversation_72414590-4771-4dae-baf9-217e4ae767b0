using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Infrastructure.Database.Context;

namespace EmailProcessor.Infrastructure.Database.Repositories
{
    public class ConfigurationRepository : IConfigurationRepository
    {
        private readonly EmailProcessorContext _context;

        public ConfigurationRepository(EmailProcessorContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Configuration> GetByKeyAsync(string configKey)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            return await _context.Configuration
                .FirstOrDefaultAsync(c => c.ConfigKey == configKey);
        }

        public async Task<IEnumerable<Configuration>> GetAllAsync()
        {
            return await _context.Configuration
                .OrderBy(c => c.ConfigKey)
                .ToListAsync();
        }

        public async Task<Configuration> AddAsync(Configuration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            // Check if configuration with the same key already exists
            var existingConfig = await GetByKeyAsync(configuration.ConfigKey);
            if (existingConfig != null)
                throw new InvalidOperationException($"Configuration with key '{configuration.ConfigKey}' already exists");

            await _context.Configuration.AddAsync(configuration);
            await _context.SaveChangesAsync();
            return configuration;
        }

        public async Task<Configuration> UpdateAsync(Configuration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            var existingConfig = await GetByKeyAsync(configuration.ConfigKey);
            if (existingConfig == null)
                throw new InvalidOperationException($"Configuration with key '{configuration.ConfigKey}' not found");

            // Update the existing configuration
            existingConfig.UpdateValue(configuration.ConfigValue);
            if (!string.IsNullOrEmpty(configuration.Description))
                existingConfig.UpdateDescription(configuration.Description);
            existingConfig.SetEncrypted(configuration.IsEncrypted);

            _context.Configuration.Update(existingConfig);
            await _context.SaveChangesAsync();
            return existingConfig;
        }

        public async Task<bool> DeleteAsync(string configKey)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            var configuration = await GetByKeyAsync(configKey);
            if (configuration == null)
                return false;

            _context.Configuration.Remove(configuration);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(string configKey)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            return await _context.Configuration.AnyAsync(c => c.ConfigKey == configKey);
        }

        public async Task<IEnumerable<Configuration>> GetByEncryptedStatusAsync(bool isEncrypted)
        {
            return await _context.Configuration
                .Where(c => c.IsEncrypted == isEncrypted)
                .OrderBy(c => c.ConfigKey)
                .ToListAsync();
        }

        public async Task<Configuration> GetOrCreateAsync(string configKey, string defaultValue, string description = null, bool isEncrypted = false)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            var existingConfig = await GetByKeyAsync(configKey);
            if (existingConfig != null)
                return existingConfig;

            // Create new configuration if it doesn't exist
            var newConfig = new Configuration(configKey, defaultValue, description ?? string.Empty, isEncrypted);
            await _context.Configuration.AddAsync(newConfig);
            await _context.SaveChangesAsync();
            return newConfig;
        }

        public async Task<bool> UpdateValueAsync(string configKey, string newValue)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            var configuration = await GetByKeyAsync(configKey);
            if (configuration == null)
                return false;

            configuration.UpdateValue(newValue);
            _context.Configuration.Update(configuration);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateDescriptionAsync(string configKey, string newDescription)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            var configuration = await GetByKeyAsync(configKey);
            if (configuration == null)
                return false;

            configuration.UpdateDescription(newDescription);
            _context.Configuration.Update(configuration);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> SetEncryptedStatusAsync(string configKey, bool isEncrypted)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            var configuration = await GetByKeyAsync(configKey);
            if (configuration == null)
                return false;

            configuration.SetEncrypted(isEncrypted);
            _context.Configuration.Update(configuration);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Configuration>> GetByKeyPatternAsync(string pattern)
        {
            if (string.IsNullOrWhiteSpace(pattern))
                throw new ArgumentException("Pattern cannot be null or empty", nameof(pattern));

            return await _context.Configuration
                .Where(c => c.ConfigKey.Contains(pattern))
                .OrderBy(c => c.ConfigKey)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync()
        {
            return await _context.Configuration.CountAsync();
        }

        public async Task<int> GetEncryptedCountAsync()
        {
            return await _context.Configuration.CountAsync(c => c.IsEncrypted);
        }

        public async Task<DateTime?> GetLastModifiedDateAsync()
        {
            return await _context.Configuration
                .MaxAsync(c => (DateTime?)c.ModifiedDate);
        }

        public async Task<IEnumerable<Configuration>> GetRecentlyModifiedAsync(int count = 10)
        {
            if (count <= 0)
                throw new ArgumentException("Count must be greater than zero", nameof(count));

            return await _context.Configuration
                .OrderByDescending(c => c.ModifiedDate)
                .Take(count)
                .ToListAsync();
        }
    }
} 