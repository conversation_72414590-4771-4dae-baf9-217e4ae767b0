using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Infrastructure.Storage.DualStorageService
{
    public class DualStorageService
    {
        private readonly IStorageProvider _localStorageProvider;
        private readonly IStorageProvider _uncStorageProvider;
        private readonly ILoggingProvider _loggingProvider;

        public DualStorageService(
            IStorageProvider localStorageProvider,
            IStorageProvider uncStorageProvider,
            ILoggingProvider loggingProvider)
        {
            _localStorageProvider = localStorageProvider ?? throw new ArgumentNullException(nameof(localStorageProvider));
            _uncStorageProvider = uncStorageProvider ?? throw new ArgumentNullException(nameof(uncStorageProvider));
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            
            // Validate that we have one local and one UNC provider
            if (_localStorageProvider.StorageType != StorageType.Local)
                throw new ArgumentException("First storage provider must be Local type", nameof(localStorageProvider));
            if (_uncStorageProvider.StorageType != StorageType.Unc)
                throw new ArgumentException("Second storage provider must be UNC type", nameof(uncStorageProvider));
        }

        public async Task<DualStorageResult> SaveFileAsync(string fileName, byte[] fileData, string subDirectory = null)
        {
            var correlationId = Guid.NewGuid();
            var result = new DualStorageResult
            {
                FileName = fileName,
                SubDirectory = subDirectory,
                CorrelationId = correlationId,
                LocalStorageResult = new StorageResult { StorageType = StorageType.Local },
                UncStorageResult = new StorageResult { StorageType = StorageType.Unc }
            };

            try
            {
                // Start both storage operations in parallel
                var localTask = SaveToLocalStorageAsync(fileName, fileData, subDirectory, correlationId);
                var uncTask = SaveToUncStorageAsync(fileName, fileData, subDirectory, correlationId);

                // Wait for both operations to complete
                await Task.WhenAll(localTask, uncTask);

                // Get results
                result.LocalStorageResult = await localTask;
                result.UncStorageResult = await uncTask;

                // Determine overall success
                result.IsSuccessful = result.LocalStorageResult.IsSuccessful || result.UncStorageResult.IsSuccessful;

                if (result.IsSuccessful)
                {
                    _loggingProvider.LogInformation($"Dual storage save completed for file: {fileName}. Local: {result.LocalStorageResult.IsSuccessful}, UNC: {result.UncStorageResult.IsSuccessful}", correlationId: correlationId);
                }
                else
                {
                    _loggingProvider.LogError($"Dual storage save failed for file: {fileName}. Both local and UNC storage failed.", correlationId: correlationId);
                }
            }
            catch (Exception ex)
            {
                _loggingProvider.LogError($"Unexpected error during dual storage save for file: {fileName}", ex, correlationId: correlationId);
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        public async Task<DualStorageResult> SaveFileAsync(string fileName, Stream fileStream, string subDirectory = null)
        {
            var correlationId = Guid.NewGuid();
            var result = new DualStorageResult
            {
                FileName = fileName,
                SubDirectory = subDirectory,
                CorrelationId = correlationId,
                LocalStorageResult = new StorageResult { StorageType = StorageType.Local },
                UncStorageResult = new StorageResult { StorageType = StorageType.Unc }
            };

            _loggingProvider.LogInformation($"Starting dual storage save for file: {fileName}", correlationId: correlationId);

            try
            {
                // Read the stream into memory for parallel processing
                using var memoryStream = new MemoryStream();
                await fileStream.CopyToAsync(memoryStream);
                var fileData = memoryStream.ToArray();

                // Reset stream position for potential retry
                fileStream.Position = 0;

                // Start both storage operations in parallel
                var localTask = SaveToLocalStorageAsync(fileName, fileData, subDirectory, correlationId);
                var uncTask = SaveToUncStorageAsync(fileName, fileData, subDirectory, correlationId);

                // Wait for both operations to complete
                await Task.WhenAll(localTask, uncTask);

                // Get results
                result.LocalStorageResult = await localTask;
                result.UncStorageResult = await uncTask;

                // Determine overall success
                result.IsSuccessful = result.LocalStorageResult.IsSuccessful || result.UncStorageResult.IsSuccessful;

                if (result.IsSuccessful)
                {
                    _loggingProvider.LogInformation($"Dual storage save completed for file: {fileName}. Local: {result.LocalStorageResult.IsSuccessful}, UNC: {result.UncStorageResult.IsSuccessful}", correlationId: correlationId);
                }
                else
                {
                    _loggingProvider.LogError($"Dual storage save failed for file: {fileName}. Both local and UNC storage failed.", correlationId: correlationId);
                }
            }
            catch (Exception ex)
            {
                _loggingProvider.LogError($"Unexpected error during dual storage save for file: {fileName}", ex, correlationId: correlationId);
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        public async Task<bool> TestConnectivityAsync()
        {
            var localConnectivity = await _localStorageProvider.TestConnectivityAsync();
            var uncConnectivity = await _uncStorageProvider.TestConnectivityAsync();

            _loggingProvider.LogInformation($"Connectivity test results - Local: {localConnectivity}, UNC: {uncConnectivity}");

            return localConnectivity && uncConnectivity;
        }

        public async Task<StorageHealthStatus> GetStorageHealthStatusAsync()
        {
            var localAvailableSpace = await _localStorageProvider.GetAvailableSpaceAsync();
            var uncAvailableSpace = await _uncStorageProvider.GetAvailableSpaceAsync();

            return new StorageHealthStatus
            {
                LocalStorageAvailable = await _localStorageProvider.TestConnectivityAsync(),
                UncStorageAvailable = await _uncStorageProvider.TestConnectivityAsync(),
                LocalAvailableSpace = localAvailableSpace,
                UncAvailableSpace = uncAvailableSpace,
                Timestamp = DateTime.UtcNow
            };
        }

        private async Task<StorageResult> SaveToLocalStorageAsync(string fileName, byte[] fileData, string subDirectory, Guid correlationId)
        {
            var result = new StorageResult { StorageType = StorageType.Local };

            try
            {
                _loggingProvider.LogDebug($"Starting local storage save for file: {fileName}", correlationId: correlationId);

                var filePath = await _localStorageProvider.SaveFileAsync(fileData, fileName, subDirectory);
                result.IsSuccessful = true;
                result.FilePath = filePath.Value;

                var fileInfo = await _localStorageProvider.GetFileInfoAsync(filePath.Value);
                result.FileSize = fileInfo.Length;
                
                _loggingProvider.LogInformation($"Local storage save successful for file: {fileName}, Size: {result.FileSize} bytes", correlationId: correlationId);
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                _loggingProvider.LogError($"Exception during local storage save for file: {fileName}", ex, correlationId: correlationId);
            }

            return result;
        }

        private async Task<StorageResult> SaveToUncStorageAsync(string fileName, byte[] fileData, string subDirectory, Guid correlationId)
        {
            var result = new StorageResult { StorageType = StorageType.Unc };

            try
            {
                _loggingProvider.LogDebug($"Starting UNC storage save for file: {fileName}", correlationId: correlationId);

                var filePath = await _uncStorageProvider.SaveFileAsync(fileData, fileName, subDirectory);
                result.IsSuccessful = true;
                result.FilePath = filePath.Value;

                var fileInfo = await _uncStorageProvider.GetFileInfoAsync(filePath.Value);
                result.FileSize = fileInfo.Length;
                
                _loggingProvider.LogInformation($"UNC storage save successful for file: {fileName}, Size: {result.FileSize} bytes", correlationId: correlationId);
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                _loggingProvider.LogError($"Exception during UNC storage save for file: {fileName}", ex, correlationId: correlationId);
            }

            return result;
        }
    }

    public class DualStorageResult
    {
        public string FileName { get; set; }
        public string SubDirectory { get; set; }
        public Guid CorrelationId { get; set; }
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public StorageResult LocalStorageResult { get; set; }
        public StorageResult UncStorageResult { get; set; }
    }

    public class StorageResult
    {
        public StorageType StorageType { get; set; }
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public long FileSize { get; set; }
        public string FilePath { get; set; }
    }

    public class StorageHealthStatus
    {
        public bool LocalStorageAvailable { get; set; }
        public bool UncStorageAvailable { get; set; }
        public long LocalAvailableSpace { get; set; }
        public long UncAvailableSpace { get; set; }
        public DateTime Timestamp { get; set; }
    }
} 