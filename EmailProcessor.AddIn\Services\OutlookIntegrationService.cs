using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;

namespace EmailProcessor.AddIn.Services
{
    /// <summary>
    /// Service for integrating with Outlook and extracting email/attachment data
    /// </summary>
    public class OutlookIntegrationService : IOutlookIntegration
    {
        private readonly IVSTOConfiguration _configuration;

        public OutlookIntegrationService(IVSTOConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public EmailData ExtractEmailData(dynamic mailItem)
        {
            try
            {
                var emailData = new EmailData
                {
                    Subject = GetPropertyValue(mailItem, "Subject") ?? "",
                    SenderName = GetPropertyValue(mailItem, "SenderName") ?? "",
                    SenderEmail = GetPropertyValue(mailItem, "SenderEmailAddress") ?? "",
                    EmailType = GetEmailType(mailItem),
                    Timestamp = GetDateTimeProperty(mailItem, "ReceivedTime"),
                    OutlookMessageId = GetPropertyValue(mailItem, "EntryID") ?? "",
                    EntryId = GetPropertyValue(mailItem, "EntryID") ?? "",
                    ConversationId = GetPropertyValue(mailItem, "ConversationID") ?? "",
                    HasAttachments = GetBoolProperty(mailItem, "Attachments.Count") > 0,
                    AttachmentCount = GetIntProperty(mailItem, "Attachments.Count")
                };

                // Extract recipients
                emailData.RecipientTo = ExtractRecipients(mailItem, "To");
                emailData.RecipientCC = ExtractRecipients(mailItem, "CC");

                return emailData;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to extract email data: {ex.Message}", ex);
            }
        }

        public List<AttachmentData> ExtractAttachmentData(dynamic mailItem, long maxFileSize = 100 * 1024 * 1024)
        {
            var attachments = new List<AttachmentData>();

            try
            {
                var attachmentCount = GetIntProperty(mailItem, "Attachments.Count");
                
                for (int i = 1; i <= attachmentCount; i++)
                {
                    try
                    {
                        var attachment = mailItem.Attachments[i];
                        
                        if (!ShouldProcessAttachment(attachment))
                            continue;

                        var attachmentData = ExtractSingleAttachment(attachment, maxFileSize);
                        if (attachmentData != null)
                        {
                            attachments.Add(attachmentData);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue with other attachments
                        System.Diagnostics.Debug.WriteLine($"Error extracting attachment {i}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to extract attachment data: {ex.Message}", ex);
            }

            return attachments;
        }

        public bool ShouldProcessEmail(dynamic mailItem)
        {
            try
            {
                // Check if email type should be processed
                var emailType = GetEmailType(mailItem);
                if (emailType == "Received" && !_configuration.ProcessReceivedEmails)
                    return false;
                if (emailType == "Sent" && !_configuration.ProcessSentEmails)
                    return false;

                // Check if email has attachments
                var hasAttachments = GetBoolProperty(mailItem, "Attachments.Count") > 0;
                if (!hasAttachments)
                    return false;

                // Check if email is not null or empty
                var subject = GetPropertyValue(mailItem, "Subject");
                if (string.IsNullOrWhiteSpace(subject))
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool ShouldProcessAttachment(dynamic attachment)
        {
            try
            {
                // Check if attachment is inline (embedded in email body)
                var isInline = GetBoolProperty(attachment, "Inline");
                if (isInline)
                    return false;

                // Check file extension
                var fileName = GetPropertyValue(attachment, "FileName");
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
                if (_configuration.ExcludedFileExtensions.Contains(fileExtension))
                    return false;

                // Check file size
                var fileSize = GetLongProperty(attachment, "Size");
                if (fileSize > _configuration.MaxFileSizeBytes)
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public string GetEmailType(dynamic mailItem)
        {
            try
            {
                // Check if this is a sent item
                var sent = GetBoolProperty(mailItem, "Sent");
                return sent ? "Sent" : "Received";
            }
            catch (Exception)
            {
                return "Received"; // Default to received
            }
        }

        private AttachmentData ExtractSingleAttachment(dynamic attachment, long maxFileSize)
        {
            try
            {
                var fileName = GetPropertyValue(attachment, "FileName");
                var originalFileName = GetPropertyValue(attachment, "DisplayName") ?? fileName;
                var contentType = GetPropertyValue(attachment, "Type") ?? "application/octet-stream";
                var fileSize = GetLongProperty(attachment, "Size");
                var isInline = GetBoolProperty(attachment, "Inline");
                var contentId = GetPropertyValue(attachment, "ContentID") ?? "";

                // Validate file size
                if (fileSize > maxFileSize)
                {
                    System.Diagnostics.Debug.WriteLine($"Attachment {fileName} exceeds maximum file size: {fileSize} bytes");
                    return null;
                }

                // Extract file data
                var fileData = ExtractAttachmentFileData(attachment, fileSize);
                if (fileData == null)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to extract file data for attachment: {fileName}");
                    return null;
                }

                var attachmentData = new AttachmentData
                {
                    FileName = fileName,
                    OriginalFileName = originalFileName,
                    ContentType = contentType,
                    FileExtension = Path.GetExtension(fileName),
                    FileSize = fileSize,
                    FileData = Convert.ToBase64String(fileData),
                    AttachmentId = Guid.NewGuid().ToString(),
                    IsInline = isInline,
                    ContentId = contentId
                };

                return attachmentData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting single attachment: {ex.Message}");
                return null;
            }
        }

        private byte[] ExtractAttachmentFileData(dynamic attachment, long fileSize)
        {
            try
            {
                // Create temporary file to extract attachment
                var tempFile = Path.GetTempFileName();
                
                try
                {
                    // Save attachment to temporary file
                    attachment.SaveAsFile(tempFile);
                    
                    // Read file data
                    if (File.Exists(tempFile))
                    {
                        var fileData = File.ReadAllBytes(tempFile);
                        
                        // Validate file size
                        if (fileData.Length == fileSize)
                        {
                            return fileData;
                        }
                    }
                }
                finally
                {
                    // Clean up temporary file
                    try
                    {
                        if (File.Exists(tempFile))
                        {
                            File.Delete(tempFile);
                        }
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting attachment file data: {ex.Message}");
                return null;
            }
        }

        private List<string> ExtractRecipients(dynamic mailItem, string recipientType)
        {
            var recipients = new List<string>();

            try
            {
                var recipientList = mailItem.Recipients;
                if (recipientList != null)
                {
                    var count = GetIntProperty(recipientList, "Count");
                    
                    for (int i = 1; i <= count; i++)
                    {
                        try
                        {
                            var recipient = recipientList[i];
                            var type = GetIntProperty(recipient, "Type");
                            
                            // Type 1 = To, Type 2 = CC, Type 3 = BCC
                            if ((recipientType == "To" && type == 1) ||
                                (recipientType == "CC" && type == 2))
                            {
                                var emailAddress = GetPropertyValue(recipient, "Address");
                                if (!string.IsNullOrWhiteSpace(emailAddress))
                                {
                                    recipients.Add(emailAddress);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error extracting recipient {i}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting {recipientType} recipients: {ex.Message}");
            }

            return recipients;
        }

        private string GetPropertyValue(dynamic obj, string propertyName)
        {
            try
            {
                var property = obj.GetType().GetProperty(propertyName);
                if (property != null)
                {
                    var value = property.GetValue(obj);
                    return value != null ? value.ToString() : null;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private int GetIntProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                int result;
                if (int.TryParse(value, out result))
                {
                    return result;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private long GetLongProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                long result;
                if (long.TryParse(value, out result))
                {
                    return result;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private bool GetBoolProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                bool result;
                if (bool.TryParse(value, out result))
                {
                    return result;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private DateTime GetDateTimeProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                DateTime result;
                if (DateTime.TryParse(value, out result))
                {
                    return result;
                }
                return DateTime.UtcNow;
            }
            catch
            {
                return DateTime.UtcNow;
            }
        }
    }
} 