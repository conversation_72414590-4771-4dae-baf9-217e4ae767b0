using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Service.Services
{
    /// <summary>
    /// Service responsible for handling attachment processing operations
    /// </summary>
    public class AttachmentHandlerService
    {
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IProcessingLogRepository _processingLogRepository;
        private readonly ILoggingProvider _loggingProvider;
        private readonly DualStorageService _dualStorageService;

        public AttachmentHandlerService(
            IAttachmentRepository attachmentRepository,
            IProcessingLogRepository processingLogRepository,
            ILoggingProvider loggingProvider,
            DualStorageService dualStorageService)
        {
            _attachmentRepository = attachmentRepository ?? throw new ArgumentNullException(nameof(attachmentRepository));
            _processingLogRepository = processingLogRepository ?? throw new ArgumentNullException(nameof(processingLogRepository));
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            _dualStorageService = dualStorageService ?? throw new ArgumentNullException(nameof(dualStorageService));
        }

        /// <summary>
        /// Processes a single attachment with dual storage
        /// </summary>
        public async Task<AttachmentProcessingResult> ProcessAttachmentAsync(
            Attachment attachment, 
            byte[] fileData, 
            string subDirectory, 
            Guid correlationId)
        {
            var result = new AttachmentProcessingResult
            {
                FileName = attachment.FileName,
                AttachmentId = attachment.AttachmentId
            };

            try
            {
                await _loggingProvider.LogInformationAsync($"Processing attachment: {attachment.FileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);

                // Validate file data
                if (fileData == null || fileData.Length == 0)
                {
                    throw new ArgumentException("File data is null or empty");
                }

                // Validate file size
                if (fileData.Length != attachment.FileSize)
                {
                    throw new ArgumentException($"File size mismatch. Expected: {attachment.FileSize}, Actual: {fileData.Length}");
                }

                // Save to dual storage
                var storageResult = await _dualStorageService.SaveFileAsync(
                    attachment.FileName,
                    fileData,
                    subDirectory);

                // Update attachment based on storage results
                await UpdateAttachmentWithStorageResultsAsync(attachment, storageResult, correlationId);

                // Set result based on storage success
                result.Success = storageResult.IsSuccessful;
                result.LocalStoragePath = storageResult.LocalStorageResult.FilePath;
                result.UncStoragePath = storageResult.UncStorageResult.FilePath;

                if (storageResult.IsSuccessful)
                {
                    await _loggingProvider.LogInformationAsync($"Attachment processed successfully: {attachment.FileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
                }
                else
                {
                    result.ErrorMessage = $"Storage failed. Local: {storageResult.LocalStorageResult.IsSuccessful}, UNC: {storageResult.UncStorageResult.IsSuccessful}";
                    await _loggingProvider.LogErrorAsync($"Attachment storage failed: {attachment.FileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", null, correlationId);
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;

                await _loggingProvider.LogErrorAsync($"Error processing attachment: {attachment.FileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", ex, correlationId);
            }

            return result;
        }

        /// <summary>
        /// Retries processing for failed attachments
        /// </summary>
        public async Task<List<AttachmentProcessingResult>> RetryFailedAttachmentsAsync(
            long emailId, 
            int maxRetries = 3, 
            Guid? correlationId = null)
        {
            correlationId ??= Guid.NewGuid();
            var results = new List<AttachmentProcessingResult>();

            try
            {
                await _loggingProvider.LogInformationAsync($"Retrying failed attachments for email: {emailId} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);

                // Get failed attachments
                var allAttachments = await _attachmentRepository.GetByEmailIdAsync(emailId);
            var failedAttachments = allAttachments.Where(a => a.HasProcessingErrors).ToList();
                
                if (!failedAttachments.Any())
                {
                    await _loggingProvider.LogInformationAsync($"No failed attachments found for email: {emailId} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
                    return results;
                }

                await _loggingProvider.LogInformationAsync($"Found {failedAttachments.Count()} failed attachments to retry (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);

                // Process each failed attachment
                foreach (var attachment in failedAttachments)
                {
                    var retryResult = await RetrySingleAttachmentAsync(attachment, maxRetries, correlationId.Value);
                    results.Add(retryResult);
                }

                await _loggingProvider.LogInformationAsync($"Retry processing completed. Successful: {results.Count(r => r.Success)} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error during retry processing for email: {emailId} (CorrelationId: {correlationId})", "AttachmentHandlerService", ex, correlationId);
            }

            return results;
        }

        /// <summary>
        /// Gets processing statistics for an email
        /// </summary>
        public async Task<EmailProcessingStatistics> GetProcessingStatisticsAsync(long emailId, Guid? correlationId = null)
        {
            correlationId ??= Guid.NewGuid();

            try
            {
                var attachments = await _attachmentRepository.GetByEmailIdAsync(emailId);
                
                var statistics = new EmailProcessingStatistics
                {
                    EmailId = emailId,
                    TotalAttachments = attachments.Count(),
                    SuccessfulAttachments = attachments.Count(a => a.IsFullyProcessed),
                    FailedAttachments = attachments.Count(a => a.HasProcessingErrors),
                    PendingAttachments = attachments.Count(a => 
                        a.LocalProcessingStatus == ProcessingStatus.Pending || 
                        a.UncProcessingStatus == ProcessingStatus.Pending),
                    ProcessingAttachments = attachments.Count(a => 
                        a.LocalProcessingStatus == ProcessingStatus.Processing || 
                        a.UncProcessingStatus == ProcessingStatus.Processing)
                };

                await _loggingProvider.LogDebugAsync($"Processing statistics for email {emailId}: {statistics.SuccessfulAttachments}/{statistics.TotalAttachments} successful (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);

                return statistics;
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error getting processing statistics for email: {emailId} (CorrelationId: {correlationId})", "AttachmentHandlerService", ex, correlationId);
                throw;
            }
        }

        /// <summary>
        /// Validates attachment data and metadata
        /// </summary>
        public async Task<AttachmentValidationResult> ValidateAttachmentAsync(
            string fileName, 
            string contentType, 
            long fileSize, 
            byte[] fileData, 
            Guid? correlationId = null)
        {
            correlationId ??= Guid.NewGuid();
            var result = new AttachmentValidationResult { IsValid = true };

            try
            {
                await _loggingProvider.LogDebugAsync($"Validating attachment: {fileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);

                // Validate file name
                if (string.IsNullOrWhiteSpace(fileName))
                {
                    result.IsValid = false;
                    result.Errors.Add("File name cannot be null or empty");
                }

                // Validate content type
                if (string.IsNullOrWhiteSpace(contentType))
                {
                    result.IsValid = false;
                    result.Errors.Add("Content type cannot be null or empty");
                }

                // Validate file size
                if (fileSize <= 0)
                {
                    result.IsValid = false;
                    result.Errors.Add("File size must be greater than zero");
                }

                // Validate file data
                if (fileData == null || fileData.Length == 0)
                {
                    result.IsValid = false;
                    result.Errors.Add("File data cannot be null or empty");
                }
                else if (fileData.Length != fileSize)
                {
                    result.IsValid = false;
                    result.Errors.Add($"File size mismatch. Expected: {fileSize}, Actual: {fileData.Length}");
                }

                // Check for potentially dangerous file types
                var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
                var dangerousExtensions = new[] { ".exe", ".bat", ".cmd", ".scr", ".pif", ".com" };
                if (dangerousExtensions.Contains(fileExtension))
                {
                    result.IsValid = false;
                    result.Errors.Add($"File type '{fileExtension}' is not allowed for security reasons");
                }

                if (result.IsValid)
                {
                    await _loggingProvider.LogDebugAsync($"Attachment validation passed: {fileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
                }
                else
                {
                    await _loggingProvider.LogWarningAsync($"Attachment validation failed: {fileName}. Errors: {string.Join(", ", result.Errors)} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Validation error: {ex.Message}");
                await _loggingProvider.LogErrorAsync($"Error validating attachment: {fileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", ex, correlationId);
            }

            return result;
        }

        private async Task UpdateAttachmentWithStorageResultsAsync(
            Attachment attachment, 
            DualStorageResult storageResult, 
            Guid correlationId)
        {
            try
            {
                // Update local storage status
                if (storageResult.LocalStorageResult.IsSuccessful)
                {
                    attachment.SetLocalStoragePath(FilePath.Create(storageResult.LocalStorageResult.FilePath));
                    attachment.UpdateLocalProcessingStatus(ProcessingStatus.Completed);
                }
                else
                {
                    attachment.UpdateLocalProcessingStatus(ProcessingStatus.Failed, storageResult.LocalStorageResult.ErrorMessage);
                }

                // Update UNC storage status
                if (storageResult.UncStorageResult.IsSuccessful)
                {
                    attachment.SetUncStoragePath(FilePath.Create(storageResult.UncStorageResult.FilePath));
                    attachment.UpdateUncProcessingStatus(ProcessingStatus.Completed);
                }
                else
                {
                    attachment.UpdateUncProcessingStatus(ProcessingStatus.Failed, storageResult.UncStorageResult.ErrorMessage);
                }

                // Save changes
                await _attachmentRepository.UpdateAsync(attachment);

                await _loggingProvider.LogDebugAsync($"Updated attachment {attachment.AttachmentId} with storage results (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error updating attachment with storage results (CorrelationId: {correlationId})", "AttachmentHandlerService", ex, correlationId);
                throw;
            }
        }

        private async Task<AttachmentProcessingResult> RetrySingleAttachmentAsync(
            Attachment attachment, 
            int maxRetries, 
            Guid correlationId)
        {
            var result = new AttachmentProcessingResult
            {
                FileName = attachment.FileName,
                AttachmentId = attachment.AttachmentId
            };

            try
            {
                await _loggingProvider.LogInformationAsync($"Retrying attachment: {attachment.FileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);

                // For retry, we would need the original file data
                // In a real implementation, you might store this temporarily or retrieve it from a cache
                // For now, we'll just update the status to indicate retry was attempted
                
                result.Success = false;
                result.ErrorMessage = "Retry functionality requires original file data which is not available in this implementation";

                await _loggingProvider.LogWarningAsync($"Retry attempted for {attachment.FileName} but original file data not available (CorrelationId: {correlationId})", "AttachmentHandlerService", correlationId);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                await _loggingProvider.LogErrorAsync($"Error during retry for attachment: {attachment.FileName} (CorrelationId: {correlationId})", "AttachmentHandlerService", ex, correlationId);
            }

            return result;
        }
    }

    /// <summary>
    /// Result of attachment processing
    /// </summary>
    public class AttachmentProcessingResult
    {
        public string FileName { get; set; } = string.Empty;
        public long? AttachmentId { get; set; }
        public bool Success { get; set; }
        public string? LocalStoragePath { get; set; }
        public string? UncStoragePath { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Email processing statistics
    /// </summary>
    public class EmailProcessingStatistics
    {
        public long EmailId { get; set; }
        public int TotalAttachments { get; set; }
        public int SuccessfulAttachments { get; set; }
        public int FailedAttachments { get; set; }
        public int PendingAttachments { get; set; }
        public int ProcessingAttachments { get; set; }
    }

    /// <summary>
    /// Result of attachment validation
    /// </summary>
    public class AttachmentValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }
} 