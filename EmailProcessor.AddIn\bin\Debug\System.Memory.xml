<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Memory</name>
    </assembly>
    <members>
        <member name="T:System.Buffers.Binary.BinaryPrimitives">
            <summary>
            Reads bytes as primitives with specific endianness
            </summary>
            <remarks>
            For native formats, MemoryExtensions.Read{T}; should be used.
            Use these helpers when you need to read specific endinanness.
            </remarks>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.SByte)">
            <summary>
            This is a no-op and added only for consistency.
            This allows the caller to read a struct of numeric primitives and reverse each field
            rather than having to skip sbyte fields.
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Int16)">
            <summary>
            Reverses a primitive value - performs an endianness swap
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Int32)">
            <summary>
            Reverses a primitive value - performs an endianness swap
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Int64)">
            <summary>
            Reverses a primitive value - performs an endianness swap
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Byte)">
            <summary>
            This is a no-op and added only for consistency.
            This allows the caller to read a struct of numeric primitives and reverse each field
            rather than having to skip byte fields.
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.UInt16)">
            <summary>
            Reverses a primitive value - performs an endianness swap
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.UInt32)">
            <summary>
            Reverses a primitive value - performs an endianness swap
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.UInt64)">
            <summary>
            Reverses a primitive value - performs an endianness swap
            </summary> 
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt16BigEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads an Int16 out of a read-only span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt32BigEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads an Int32 out of a read-only span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt64BigEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads an Int64 out of a read-only span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt16BigEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a UInt16 out of a read-only span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt32BigEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a UInt32 out of a read-only span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt64BigEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a UInt64 out of a read-only span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt16BigEndian(System.ReadOnlySpan{System.Byte},System.Int16@)">
            <summary>
            Reads an Int16 out of a read-only span of bytes as big endian.
            <returns>If the span is too small to contain an Int16, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt32BigEndian(System.ReadOnlySpan{System.Byte},System.Int32@)">
            <summary>
            Reads an Int32 out of a read-only span of bytes as big endian.
            <returns>If the span is too small to contain an Int32, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt64BigEndian(System.ReadOnlySpan{System.Byte},System.Int64@)">
            <summary>
            Reads an Int64 out of a read-only span of bytes as big endian.
            <returns>If the span is too small to contain an Int64, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt16BigEndian(System.ReadOnlySpan{System.Byte},System.UInt16@)">
            <summary>
            Reads a UInt16 out of a read-only span of bytes as big endian.
            <returns>If the span is too small to contain a UInt16, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt32BigEndian(System.ReadOnlySpan{System.Byte},System.UInt32@)">
            <summary>
            Reads a UInt32 out of a read-only span of bytes as big endian.
            <returns>If the span is too small to contain a UInt32, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt64BigEndian(System.ReadOnlySpan{System.Byte},System.UInt64@)">
            <summary>
            Reads a UInt64 out of a read-only span of bytes as big endian.
            <returns>If the span is too small to contain a UInt64, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt16LittleEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads an Int16 out of a read-only span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt32LittleEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads an Int32 out of a read-only span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt64LittleEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads an Int64 out of a read-only span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt16LittleEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a UInt16 out of a read-only span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt32LittleEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a UInt32 out of a read-only span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt64LittleEndian(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a UInt64 out of a read-only span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt16LittleEndian(System.ReadOnlySpan{System.Byte},System.Int16@)">
            <summary>
            Reads an Int16 out of a read-only span of bytes as little endian.
            <returns>If the span is too small to contain an Int16, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt32LittleEndian(System.ReadOnlySpan{System.Byte},System.Int32@)">
            <summary>
            Reads an Int32 out of a read-only span of bytes as little endian.
            <returns>If the span is too small to contain an Int32, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt64LittleEndian(System.ReadOnlySpan{System.Byte},System.Int64@)">
            <summary>
            Reads an Int64 out of a read-only span of bytes as little endian.
            <returns>If the span is too small to contain an Int64, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt16LittleEndian(System.ReadOnlySpan{System.Byte},System.UInt16@)">
            <summary>
            Reads a UInt16 out of a read-only span of bytes as little endian.
            <returns>If the span is too small to contain a UInt16, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt32LittleEndian(System.ReadOnlySpan{System.Byte},System.UInt32@)">
            <summary>
            Reads a UInt32 out of a read-only span of bytes as little endian.
            <returns>If the span is too small to contain a UInt32, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt64LittleEndian(System.ReadOnlySpan{System.Byte},System.UInt64@)">
            <summary>
            Reads a UInt64 out of a read-only span of bytes as little endian.
            <returns>If the span is too small to contain a UInt64, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt16BigEndian(System.Span{System.Byte},System.Int16)">
            <summary>
            Writes an Int16 into a span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt32BigEndian(System.Span{System.Byte},System.Int32)">
            <summary>
            Writes an Int32 into a span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt64BigEndian(System.Span{System.Byte},System.Int64)">
            <summary>
            Writes an Int64 into a span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt16BigEndian(System.Span{System.Byte},System.UInt16)">
            <summary>
            Write a UInt16 into a span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt32BigEndian(System.Span{System.Byte},System.UInt32)">
            <summary>
            Write a UInt32 into a span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt64BigEndian(System.Span{System.Byte},System.UInt64)">
            <summary>
            Write a UInt64 into a span of bytes as big endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt16BigEndian(System.Span{System.Byte},System.Int16)">
            <summary>
            Writes an Int16 into a span of bytes as big endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt32BigEndian(System.Span{System.Byte},System.Int32)">
            <summary>
            Writes an Int32 into a span of bytes as big endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt64BigEndian(System.Span{System.Byte},System.Int64)">
            <summary>
            Writes an Int64 into a span of bytes as big endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt16BigEndian(System.Span{System.Byte},System.UInt16)">
            <summary>
            Write a UInt16 into a span of bytes as big endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt32BigEndian(System.Span{System.Byte},System.UInt32)">
            <summary>
            Write a UInt32 into a span of bytes as big endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt64BigEndian(System.Span{System.Byte},System.UInt64)">
            <summary>
            Write a UInt64 into a span of bytes as big endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt16LittleEndian(System.Span{System.Byte},System.Int16)">
            <summary>
            Writes an Int16 into a span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt32LittleEndian(System.Span{System.Byte},System.Int32)">
            <summary>
            Writes an Int32 into a span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt64LittleEndian(System.Span{System.Byte},System.Int64)">
            <summary>
            Writes an Int64 into a span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt16LittleEndian(System.Span{System.Byte},System.UInt16)">
            <summary>
            Write a UInt16 into a span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt32LittleEndian(System.Span{System.Byte},System.UInt32)">
            <summary>
            Write a UInt32 into a span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt64LittleEndian(System.Span{System.Byte},System.UInt64)">
            <summary>
            Write a UInt64 into a span of bytes as little endian.
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt16LittleEndian(System.Span{System.Byte},System.Int16)">
            <summary>
            Writes an Int16 into a span of bytes as little endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt32LittleEndian(System.Span{System.Byte},System.Int32)">
            <summary>
            Writes an Int32 into a span of bytes as little endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt64LittleEndian(System.Span{System.Byte},System.Int64)">
            <summary>
            Writes an Int64 into a span of bytes as little endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt16LittleEndian(System.Span{System.Byte},System.UInt16)">
            <summary>
            Write a UInt16 into a span of bytes as little endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt32LittleEndian(System.Span{System.Byte},System.UInt32)">
            <summary>
            Write a UInt32 into a span of bytes as little endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt64LittleEndian(System.Span{System.Byte},System.UInt64)">
            <summary>
            Write a UInt64 into a span of bytes as little endian.
            <returns>If the span is too small to contain the value, return false.</returns>
            </summary>
        </member>
        <member name="T:System.Buffers.BuffersExtensions">
            <summary>
            Extension methods for <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.BuffersExtensions.PositionOf``1(System.Buffers.ReadOnlySequence{``0}@,``0)">
            <summary>
            Returns position of first occurrence of item in the <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.BuffersExtensions.CopyTo``1(System.Buffers.ReadOnlySequence{``0}@,System.Span{``0})">
            <summary>
            Copy the <see cref="T:System.Buffers.ReadOnlySequence`1"/> to the specified <see cref="T:System.Span`1"/>.
            </summary>
            <param name="source">The source <see cref="T:System.Buffers.ReadOnlySequence`1"/>.</param>
            <param name="destination">The destination <see cref="T:System.Span`1"/>.</param>
        </member>
        <member name="M:System.Buffers.BuffersExtensions.ToArray``1(System.Buffers.ReadOnlySequence{``0}@)">
            <summary>
            Converts the <see cref="T:System.Buffers.ReadOnlySequence`1"/> to an array
            </summary>
        </member>
        <member name="M:System.Buffers.BuffersExtensions.Write``1(System.Buffers.IBufferWriter{``0},System.ReadOnlySpan{``0})">
            <summary>
            Writes contents of <paramref name="value"/> to <paramref name="writer"/>
            </summary>
        </member>
        <member name="T:System.Buffers.IBufferWriter`1">
            <summary>
            Represents a <typeparam name="T"/> sink
            </summary>
        </member>
        <member name="M:System.Buffers.IBufferWriter`1.Advance(System.Int32)">
            <summary>
            Notifies <see cref="T:System.Buffers.IBufferWriter`1"/> that <paramref name="count"/> amount of data was written to <see cref="T:System.Span`1"/>/<see cref="T:System.Memory`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.IBufferWriter`1.GetMemory(System.Int32)">
            <summary>
            Requests the <see cref="T:System.Memory`1"/> that is at least <paramref name="sizeHint"/> in size if possible, otherwise returns maximum available memory.
            If <paramref name="sizeHint"/> is equal to <code>0</code>, currently available memory would get returned.
            </summary>
        </member>
        <member name="M:System.Buffers.IBufferWriter`1.GetSpan(System.Int32)">
            <summary>
            Requests the <see cref="T:System.Span`1"/> that is at least <paramref name="sizeHint"/> in size if possible, otherwise returns maximum available memory.
            If <paramref name="sizeHint"/> is equal to <code>0</code>, currently available memory would get returned.
            </summary>
        </member>
        <member name="T:System.Buffers.IMemoryOwner`1">
            <summary>
            Owner of Memory<typeparamref name="T"/> that is responsible for disposing the underlying memory appropriately.
            </summary>
        </member>
        <member name="P:System.Buffers.IMemoryOwner`1.Memory">
            <summary>
            Returns a Memory<typeparamref name="T"/>.
            </summary>
        </member>
        <member name="T:System.Buffers.IPinnable">
            <summary>
            Provides a mechanism for pinning and unpinning objects to prevent the GC from moving them.
            </summary>
        </member>
        <member name="M:System.Buffers.IPinnable.Pin(System.Int32)">
            <summary>
            Call this method to indicate that the IPinnable object can not be moved by the garbage collector.
            The address of the pinned object can be taken.
            <param name="elementIndex">The offset to the element within the memory at which the returned <see cref="T:System.Buffers.MemoryHandle"/> points to.</param>
            </summary>
        </member>
        <member name="M:System.Buffers.IPinnable.Unpin">
            <summary>
            Call this method to indicate that the IPinnable object no longer needs to be pinned.
            The garbage collector is free to move the object now.
            </summary>
        </member>
        <member name="T:System.Buffers.MemoryHandle">
            <summary>
            A handle for the memory.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryHandle.#ctor(System.Void*,System.Runtime.InteropServices.GCHandle,System.Buffers.IPinnable)">
            <summary>
            Creates a new memory handle for the memory.
            </summary>
            <param name="pointer">pointer to memory</param>
            <param name="pinnable">reference to manually managed object, or default if there is no memory manager</param>
            <param name="handle">handle used to pin array buffers</param>
        </member>
        <member name="P:System.Buffers.MemoryHandle.Pointer">
            <summary>
            Returns the pointer to memory, where the memory is assumed to be pinned and hence the address won't change.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryHandle.Dispose">
            <summary>
            Frees the pinned handle and releases IPinnable.
            </summary>
        </member>
        <member name="T:System.Buffers.MemoryManager`1">
            <summary>
            Manager of <see cref="T:System.Memory`1"/> that provides the implementation.
            </summary>
        </member>
        <member name="P:System.Buffers.MemoryManager`1.Memory">
            <summary>
            Returns a <see cref="T:System.Memory`1"/>.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.GetSpan">
            <summary>
            Returns a span wrapping the underlying memory.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.Pin(System.Int32)">
            <summary>
            Returns a handle to the memory that has been pinned and hence its address can be taken.
            </summary>
            <param name="elementIndex">The offset to the element within the memory at which the returned <see cref="T:System.Buffers.MemoryHandle"/> points to. (default = 0)</param>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.Unpin">
            <summary>
            Lets the garbage collector know that the object is free to be moved now.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.CreateMemory(System.Int32)">
            <summary>
            Returns a <see cref="T:System.Memory`1"/> for the current <see cref="T:System.Buffers.MemoryManager`1"/>.
            </summary>
            <param name="length">The element count in the memory, starting at offset 0.</param>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.CreateMemory(System.Int32,System.Int32)">
            <summary>
            Returns a <see cref="T:System.Memory`1"/> for the current <see cref="T:System.Buffers.MemoryManager`1"/>.
            </summary>
            <param name="start">The offset to the element which the returned memory starts at.</param>
            <param name="length">The element count in the memory, starting at element offset <paramref name="start"/>.</param>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.TryGetArray(System.ArraySegment{`0}@)">
            <summary>
            Returns an array segment.
            <remarks>Returns the default array segment if not overriden.</remarks>
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.System#IDisposable#Dispose">
            <summary>
            Implements IDisposable.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryManager`1.Dispose(System.Boolean)">
            <summary>
            Clean up of any leftover managed and unmanaged resources.
            </summary>
        </member>
        <member name="T:System.Buffers.MemoryPool`1">
            <summary>
            Represents a pool of memory blocks.
            </summary>
        </member>
        <member name="P:System.Buffers.MemoryPool`1.Shared">
            <summary>
            Returns a singleton instance of a MemoryPool based on arrays.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryPool`1.Rent(System.Int32)">
            <summary>
            Returns a memory block capable of holding at least <paramref name="minBufferSize" /> elements of T.
            </summary>
            <param name="minBufferSize">If -1 is passed, this is set to a default value for the pool.</param>
        </member>
        <member name="P:System.Buffers.MemoryPool`1.MaxBufferSize">
            <summary>
            Returns the maximum buffer size supported by this pool.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryPool`1.#ctor">
            <summary>
            Constructs a new instance of a memory pool.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryPool`1.Dispose">
            <summary>
            Frees all resources used by the memory pool.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryPool`1.Dispose(System.Boolean)">
            <summary>
            Frees all resources used by the memory pool.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:System.Buffers.OperationStatus">
            <summary>
            This enum defines the various potential status that can be returned from Span-based operations
            that support processing of input contained in multiple discontiguous buffers.
            </summary>
        </member>
        <member name="F:System.Buffers.OperationStatus.Done">
            <summary>
            The entire input buffer has been processed and the operation is complete.
            </summary>
        </member>
        <member name="F:System.Buffers.OperationStatus.DestinationTooSmall">
            <summary>
            The input is partially processed, up to what could fit into the destination buffer.
            The caller can enlarge the destination buffer, slice the buffers appropriately, and retry.
            </summary>
        </member>
        <member name="F:System.Buffers.OperationStatus.NeedMoreData">
            <summary>
            The input is partially processed, up to the last valid chunk of the input that could be consumed.
            The caller can stitch the remaining unprocessed input with more data, slice the buffers appropriately, and retry.
            </summary>
        </member>
        <member name="F:System.Buffers.OperationStatus.InvalidData">
            <summary>
            The input contained invalid bytes which could not be processed. If the input is partially processed,
            the destination contains the partial result. This guarantees that no additional data appended to the input
            will make the invalid sequence valid.
            </summary>
        </member>
        <member name="T:System.Buffers.ReadOnlySequence`1">
            <summary>
            Represents a sequence that can read a sequential series of <typeparam name="T" />.
            </summary>
        </member>
        <member name="F:System.Buffers.ReadOnlySequence`1.Empty">
            <summary>
            Returns empty <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.Length">
            <summary>
            Length of the <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.IsEmpty">
            <summary>
            Determines if the <see cref="T:System.Buffers.ReadOnlySequence`1"/> is empty.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.IsSingleSegment">
            <summary>
            Determines if the <see cref="T:System.Buffers.ReadOnlySequence`1"/> contains a single <see cref="T:System.ReadOnlyMemory`1"/> segment.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.First">
            <summary>
            Gets <see cref="T:System.ReadOnlyMemory`1"/> from the first segment.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.Start">
            <summary>
            A position to the start of the <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.End">
            <summary>
            A position to the end of the <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(System.Buffers.ReadOnlySequenceSegment{`0},System.Int32,System.Buffers.ReadOnlySequenceSegment{`0},System.Int32)">
            <summary>
            Creates an instance of <see cref="T:System.Buffers.ReadOnlySequence`1"/> from linked memory list represented by start and end segments
            and corresponding indexes in them.
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(`0[])">
            <summary>
            Creates an instance of <see cref="T:System.Buffers.ReadOnlySequence`1"/> from the <see cref="T:T[]"/>.
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(`0[],System.Int32,System.Int32)">
            <summary>
            Creates an instance of <see cref="T:System.Buffers.ReadOnlySequence`1"/> from the <see cref="T:T[]"/>, start and index.
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(System.ReadOnlyMemory{`0})">
            <summary>
            Creates an instance of <see cref="T:System.Buffers.ReadOnlySequence`1"/> from the <see cref="T:System.ReadOnlyMemory`1"/>.
            Consumer is expected to manage lifetime of memory until <see cref="T:System.Buffers.ReadOnlySequence`1"/> is not used anymore.
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int64,System.Int64)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, with <paramref name="length"/> items
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The length of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int64,System.SequencePosition)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, ending at <paramref name="end"/> (inclusive).
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="end">The end (inclusive) of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition,System.Int64)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, with <paramref name="length"/> items
            </summary>
            <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition"/> at which to begin this slice.</param>
            <param name="length">The length of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int32,System.Int32)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, with <paramref name="length"/> items
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The length of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int32,System.SequencePosition)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, ending at <paramref name="end"/> (inclusive).
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="end">The end (inclusive) of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition,System.Int32)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at '<paramref name="start"/>, with <paramref name="length"/> items
            </summary>
            <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition"/> at which to begin this slice.</param>
            <param name="length">The length of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition,System.SequencePosition)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, ending at <paramref name="end"/> (inclusive).
            </summary>
            <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition"/> at which to begin this slice.</param>
            <param name="end">The ending (inclusive) <see cref="T:System.SequencePosition"/> of the slice</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, ending at the existing <see cref="T:System.Buffers.ReadOnlySequence`1"/>'s end.
            </summary>
            <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition"/> at which to begin this slice.</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int64)">
            <summary>
            Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>, beginning at <paramref name="start"/>, ending at the existing <see cref="T:System.Buffers.ReadOnlySequence`1"/>'s end.
            </summary>
            <param name="start">The start index at which to begin this slice.</param>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.ToString">
            <inheritdoc />
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.GetEnumerator">
            <summary>
            Returns an enumerator over the <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.GetPosition(System.Int64)">
            <summary>
            Returns a new <see cref="T:System.SequencePosition"/> at an <paramref name="offset"/> from the start of the sequence.
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.GetPosition(System.Int64,System.SequencePosition)">
            <summary>
            Returns a new <see cref="T:System.SequencePosition"/> at an <paramref name="offset"/> from the <paramref name="origin"/>
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.TryGet(System.SequencePosition@,System.ReadOnlyMemory{`0}@,System.Boolean)">
            <summary>
            Tries to retrieve next segment after <paramref name="position"/> and return its contents in <paramref name="memory"/>.
            Returns <code>false</code> if end of <see cref="T:System.Buffers.ReadOnlySequence`1"/> was reached otherwise <code>true</code>.
            Sets <paramref name="position"/> to the beginning of next segment if <paramref name="advance"/> is set to <code>true</code>.
            </summary>
        </member>
        <member name="T:System.Buffers.ReadOnlySequence`1.Enumerator">
            <summary>
            An enumerator over the <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Enumerator.#ctor(System.Buffers.ReadOnlySequence{`0}@)">
            <summary>Initialize the enumerator.</summary>
            <param name="sequence">The <see cref="T:System.Buffers.ReadOnlySequence`1"/> to enumerate.</param>
        </member>
        <member name="P:System.Buffers.ReadOnlySequence`1.Enumerator.Current">
            <summary>
            The current <see cref="T:System.ReadOnlyMemory`1"/>
            </summary>
        </member>
        <member name="M:System.Buffers.ReadOnlySequence`1.Enumerator.MoveNext">
            <summary>
            Moves to the next <see cref="T:System.ReadOnlyMemory`1"/> in the <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
            <returns></returns>
        </member>
        <member name="T:System.Buffers.ReadOnlySequenceSegment`1">
            <summary>
            Represents a linked list of <see cref="T:System.ReadOnlyMemory`1"/> nodes.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequenceSegment`1.Memory">
            <summary>
            The <see cref="T:System.ReadOnlyMemory`1"/> value for current node.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequenceSegment`1.Next">
            <summary>
            The next node.
            </summary>
        </member>
        <member name="P:System.Buffers.ReadOnlySequenceSegment`1.RunningIndex">
            <summary>
            The sum of node length before current.
            </summary>
        </member>
        <member name="T:System.Buffers.StandardFormat">
            <summary>
            Represents a standard formatting string without using an actual String. A StandardFormat consists of a character (such as 'G', 'D' or 'X')
            and an optional precision ranging from 0..99, or the special value NoPrecision.
            </summary>
        </member>
        <member name="F:System.Buffers.StandardFormat.NoPrecision">
            <summary>
            Precision values for format that don't use a precision, or for when the precision is to be unspecified.
            </summary>
        </member>
        <member name="F:System.Buffers.StandardFormat.MaxPrecision">
            <summary>
            The maximum valid precision value.
            </summary>
        </member>
        <member name="P:System.Buffers.StandardFormat.Symbol">
            <summary>
            The character component of the format.
            </summary>
        </member>
        <member name="P:System.Buffers.StandardFormat.Precision">
            <summary>
            The precision component of the format. Ranges from 0..9 or the special value NoPrecision.
            </summary>
        </member>
        <member name="P:System.Buffers.StandardFormat.HasPrecision">
            <summary>
            true if Precision is a value other than NoPrecision
            </summary>
        </member>
        <member name="P:System.Buffers.StandardFormat.IsDefault">
            <summary>
            true if the StandardFormat == default(StandardFormat)
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.#ctor(System.Char,System.Byte)">
            <summary>
            Create a StandardFormat.
            </summary>
            <param name="symbol">A type-specific formatting character such as 'G', 'D' or 'X'</param>
            <param name="precision">An optional precision ranging from 0..9 or the special value NoPrecision (the default)</param>
        </member>
        <member name="M:System.Buffers.StandardFormat.op_Implicit(System.Char)~System.Buffers.StandardFormat">
            <summary>
            Converts a character to a StandardFormat using the NoPrecision precision.
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.Parse(System.ReadOnlySpan{System.Char})">
            <summary>
            Converts a classic .NET format string into a StandardFormat
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.Parse(System.String)">
            <summary>
            Converts a classic .NET format string into a StandardFormat
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.Equals(System.Object)">
            <summary>
            Returns true if both the Symbol and Precision are equal.
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.GetHashCode">
            <summary>
            Compute a hash code.
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.Equals(System.Buffers.StandardFormat)">
            <summary>
            Returns true if both the Symbol and Precision are equal.
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.ToString">
            <summary>
            Returns the format in classic .NET format.
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.op_Equality(System.Buffers.StandardFormat,System.Buffers.StandardFormat)">
            <summary>
            Returns true if both the Symbol and Precision are equal.
            </summary>
        </member>
        <member name="M:System.Buffers.StandardFormat.op_Inequality(System.Buffers.StandardFormat,System.Buffers.StandardFormat)">
            <summary>
            Returns false if both the Symbol and Precision are equal.
            </summary>
        </member>
        <member name="T:System.Buffers.Text.Base64">
            <summary>
            Convert between binary data and UTF-8 encoded text that is represented in base 64.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Base64.DecodeFromUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
             <summary>
             Decode the span of UTF-8 encoded text represented as base 64 into binary data.
             If the input is not a multiple of 4, it will decode as much as it can, to the closest multiple of 4.
            
             <param name="utf8">The input span which contains UTF-8 encoded text in base 64 that needs to be decoded.</param>
             <param name="bytes">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
             <param name="bytesConsumed">The number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary.</param>
             <param name="bytesWritten">The number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary.</param>
             <param name="isFinalBlock">True (default) when the input span contains the entire data to decode. 
             Set to false only if it is known that the input span contains partial data with more data to follow.</param>
             <returns>It returns the OperationStatus enum values:
             - Done - on successful processing of the entire input span
             - DestinationTooSmall - if there is not enough space in the output span to fit the decoded input
             - NeedMoreData - only if isFinalBlock is false and the input is not a multiple of 4, otherwise the partial input would be considered as InvalidData
             - InvalidData - if the input contains bytes outside of the expected base 64 range, or if it contains invalid/more than two padding characters,
               or if the input is incomplete (i.e. not a multiple of 4) and isFinalBlock is true.</returns>
             </summary> 
        </member>
        <member name="M:System.Buffers.Text.Base64.GetMaxDecodedFromUtf8Length(System.Int32)">
            <summary>
            Returns the maximum length (in bytes) of the result if you were to deocde base 64 encoded text within a byte span of size "length".
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="length"/> is less than 0.
            </exception>
        </member>
        <member name="M:System.Buffers.Text.Base64.DecodeFromUtf8InPlace(System.Span{System.Byte},System.Int32@)">
             <summary>
             Decode the span of UTF-8 encoded text in base 64 (in-place) into binary data.
             The decoded binary output is smaller than the text data contained in the input (the operation deflates the data).
             If the input is not a multiple of 4, it will not decode any.
            
             <param name="buffer">The input span which contains the base 64 text data that needs to be decoded.</param>
             <param name="bytesWritten">The number of bytes written into the buffer.</param>
             <returns>It returns the OperationStatus enum values:
             - Done - on successful processing of the entire input span
             - InvalidData - if the input contains bytes outside of the expected base 64 range, or if it contains invalid/more than two padding characters, 
               or if the input is incomplete (i.e. not a multiple of 4).
             It does not return DestinationTooSmall since that is not possible for base 64 decoding.
             It does not return NeedMoreData since this method tramples the data in the buffer and 
             hence can only be called once with all the data in the buffer.</returns>
             </summary> 
        </member>
        <member name="M:System.Buffers.Text.Base64.EncodeToUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
             <summary>
             Encode the span of binary data into UTF-8 encoded text represented as base 64.
            
             <param name="bytes">The input span which contains binary data that needs to be encoded.</param>
             <param name="utf8">The output span which contains the result of the operation, i.e. the UTF-8 encoded text in base 64.</param>
             <param name="bytesConsumed">The number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary.</param>
             <param name="bytesWritten">The number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary.</param>
             <param name="isFinalBlock">True (default) when the input span contains the entire data to decode. 
             Set to false only if it is known that the input span contains partial data with more data to follow.</param>
             <returns>It returns the OperationStatus enum values:
             - Done - on successful processing of the entire input span
             - DestinationTooSmall - if there is not enough space in the output span to fit the encoded input
             - NeedMoreData - only if isFinalBlock is false, otherwise the output is padded if the input is not a multiple of 3
             It does not return InvalidData since that is not possible for base 64 encoding.</returns>
             </summary> 
        </member>
        <member name="M:System.Buffers.Text.Base64.GetMaxEncodedToUtf8Length(System.Int32)">
            <summary>
            Returns the maximum length (in bytes) of the result if you were to encode binary data within a byte span of size "length".
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="length"/> is less than 0 or larger than 1610612733 (since encode inflates the data by 4/3).
            </exception>
        </member>
        <member name="M:System.Buffers.Text.Base64.EncodeToUtf8InPlace(System.Span{System.Byte},System.Int32,System.Int32@)">
             <summary>
             Encode the span of binary data (in-place) into UTF-8 encoded text represented as base 64. 
             The encoded text output is larger than the binary data contained in the input (the operation inflates the data).
            
             <param name="buffer">The input span which contains binary data that needs to be encoded. 
             It needs to be large enough to fit the result of the operation.</param>
             <param name="dataLength">The amount of binary data contained within the buffer that needs to be encoded 
             (and needs to be smaller than the buffer length).</param>
             <param name="bytesWritten">The number of bytes written into the buffer.</param>
             <returns>It returns the OperationStatus enum values:
             - Done - on successful processing of the entire buffer
             - DestinationTooSmall - if there is not enough space in the buffer beyond dataLength to fit the result of encoding the input
             It does not return NeedMoreData since this method tramples the data in the buffer and hence can only be called once with all the data in the buffer.
             It does not return InvalidData since that is not possible for base 64 encoding.</returns>
             </summary> 
        </member>
        <member name="M:System.Buffers.Text.FormattingHelpers.GetSymbolOrDefault(System.Buffers.StandardFormat@,System.Char)">
            <summary>
            Returns the symbol contained within the standard format. If the standard format
            has not been initialized, returns the provided fallback symbol.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.FormattingHelpers.FillWithAsciiZeros(System.Span{System.Byte})">
            <summary>
            Fills a buffer with the ASCII character '0' (0x30).
            </summary>
        </member>
        <member name="M:System.Buffers.Text.FormattingHelpers.WriteFourDecimalDigits(System.UInt32,System.Span{System.Byte},System.Int32)">
            <summary>
            Writes a value [ 0000 .. 9999 ] to the buffer starting at the specified offset.
            This method performs best when the starting index is a constant literal.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.FormattingHelpers.WriteTwoDecimalDigits(System.UInt32,System.Span{System.Byte},System.Int32)">
            <summary>
            Writes a value [ 00 .. 99 ] to the buffer starting at the specified offset.
            This method performs best when the starting index is a constant literal.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.FormattingHelpers.DivMod(System.UInt64,System.UInt64,System.UInt64@)">
            <summary>
            We don't have access to Math.DivRem, so this is a copy of the implementation.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.FormattingHelpers.DivMod(System.UInt32,System.UInt32,System.UInt32@)">
            <summary>
            We don't have access to Math.DivRem, so this is a copy of the implementation.
            </summary>
        </member>
        <member name="T:System.Buffers.Text.Utf8Formatter">
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
            <summary>
            Methods to format common data types as Utf8 strings.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Boolean,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Boolean as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G (default)   True/False
                l             true/false
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.DateTimeOffset,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a DateTimeOffset as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <exceptions>
            <remarks>
            Formats supported:
                default       05/25/2017 10:30:15 -08:00
                G             05/25/2017 10:30:15
                R             Tue, 03 Jan 2017 08:08:05 GMT       (RFC 1123)
                l             tue, 03 jan 2017 08:08:05 gmt       (Lowercase RFC 1123)
                O             2017-06-12T05:30:45.7680000-07:00   (Round-trippable)
            </remarks>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.DateTime,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a DateTime as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G  (default)  05/25/2017 10:30:15
                R             Tue, 03 Jan 2017 08:08:05 GMT       (RFC 1123)
                l             tue, 03 jan 2017 08:08:05 gmt       (Lowercase RFC 1123)
                O             2017-06-12T05:30:45.7680000-07:00   (Round-trippable)
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Decimal,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Decimal as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g  (default)  
                F/f             12.45       Fixed point
                E/e             1.245000e1  Exponential
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Double,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Double as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g  (default)  
                F/f             12.45       Fixed point
                E/e             1.245000e1  Exponential
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Single,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Single as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g  (default)  
                F/f             12.45       Fixed point
                E/e             1.245000e1  Exponential
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Guid,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Guid as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                D (default)     nnnnnnnn-nnnn-nnnn-nnnn-nnnnnnnnnnnn
                B               {nnnnnnnn-nnnn-nnnn-nnnn-nnnnnnnnnnnn}
                P               (nnnnnnnn-nnnn-nnnn-nnnn-nnnnnnnnnnnn)
                N               nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="T:System.Buffers.Text.Utf8Formatter.DecomposedGuid">
            <summary>
            Used to provide access to the individual bytes of a GUID.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Byte,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Byte as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.SByte,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats an SByte as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.UInt16,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a Unt16 as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Int16,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats an Int16 as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.UInt32,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a UInt32 as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Int32,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats an Int32 as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.UInt64,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a UInt64 as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Int64,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats an Int64 as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.TimeSpan,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
            <summary>
            Formats a TimeSpan as a UTF8 string.
            </summary>
            <param name="value">Value to format</param>
            <param name="destination">Buffer to write the UTF8-formatted value to</param>
            <param name="bytesWritten">Receives the length of the formatted text in bytes</param>
            <param name="format">The standard format to use</param>
            <returns>
            true for success. "bytesWritten" contains the length of the formatted text in bytes.
            false if buffer was too short. Iteratively increase the size of the buffer and retry until it succeeds. 
            </returns>
            <remarks>
            Formats supported:
                c/t/T (default) [-][d.]hh:mm:ss[.fffffff]              (constant format)
                G               [-]d:hh:mm:ss.fffffff                  (general long)
                g               [-][d:][h]h:mm:ss[.f[f[f[f[f[f[f]]]]]] (general short)
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="T:System.Buffers.Text.Utf8Parser">
            <summary>
            Methods to parse common data types to Utf8 strings.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Boolean@,System.Int32@,System.Char)">
            <summary>
            Parses a Boolean at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G (default)   True/False
                l             true/false
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.DateTime@,System.Int32@,System.Char)">
            <summary>
            Parses a DateTime at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                default       05/25/2017 10:30:15 -08:00
                G             05/25/2017 10:30:15
                R             Tue, 03 Jan 2017 08:08:05 GMT       (RFC 1123)
                l             tue, 03 jan 2017 08:08:05 gmt       (Lowercase RFC 1123)
                O             2017-06-12T05:30:45.7680000-07:00   (Round-trippable)
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.DateTimeOffset@,System.Int32@,System.Char)">
            <summary>
            Parses a DateTimeOffset at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G  (default)  05/25/2017 10:30:15
                R             Tue, 03 Jan 2017 08:08:05 GMT       (RFC 1123)
                l             tue, 03 jan 2017 08:08:05 gmt       (Lowercase RFC 1123)
                O             2017-06-12T05:30:45.7680000-07:00   (Round-trippable)
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryCreateDateTimeOffset(System.DateTime,System.Boolean,System.Int32,System.Int32,System.DateTimeOffset@)">
            <summary>
            Overflow-safe DateTimeOffset factory.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryCreateDateTimeOffset(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Int32,System.Int32,System.DateTimeOffset@)">
            <summary>
            Overflow-safe DateTimeOffset factory.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryCreateDateTimeOffsetInterpretingDataAsLocalTime(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.DateTimeOffset@)">
            <summary>
            Overflow-safe DateTimeOffset/Local time conversion factory.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryCreateDateTime(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.DateTimeKind,System.DateTime@)">
            <summary>
            Overflow-safe DateTime factory.
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Decimal@,System.Int32@,System.Char)">
            <summary>
            Parses a Decimal at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g  (default)  
                F/f             12.45       Fixed point
                E/e             1.245000e1  Exponential
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Single@,System.Int32@,System.Char)">
            <summary>
            Parses a Single at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g  (default)  
                F/f             12.45       Fixed point
                E/e             1.245000e1  Exponential
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Double@,System.Int32@,System.Char)">
            <summary>
            Parses a Double at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g  (default)  
                F/f             12.45       Fixed point
                E/e             1.245000e1  Exponential
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Guid@,System.Int32@,System.Char)">
            <summary>
            Parses a Guid at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                D (default)     nnnnnnnn-nnnn-nnnn-nnnn-nnnnnnnnnnnn
                B               {nnnnnnnn-nnnn-nnnn-nnnn-nnnnnnnnnnnn}
                P               (nnnnnnnn-nnnn-nnnn-nnnn-nnnnnnnnnnnn)
                N               nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.SByte@,System.Int32@,System.Char)">
            <summary>
            Parses a SByte at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Int16@,System.Int32@,System.Char)">
            <summary>
            Parses an Int16 at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Int32@,System.Int32@,System.Char)">
            <summary>
            Parses an Int32 at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Int64@,System.Int32@,System.Char)">
            <summary>
            Parses an Int64 at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Byte@,System.Int32@,System.Char)">
            <summary>
            Parses a Byte at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.UInt16@,System.Int32@,System.Char)">
            <summary>
            Parses a UInt16 at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@,System.Char)">
            <summary>
            Parses a UInt32 at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.UInt64@,System.Int32@,System.Char)">
            <summary>
            Parses a UInt64 at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                G/g (default)
                D/d             32767  
                N/n             32,767       
                X/x             7fff
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.TimeSpan@,System.Int32@,System.Char)">
            <summary>
            Parses a TimeSpan at the start of a Utf8 string.
            </summary>
            <param name="source">The Utf8 string to parse</param>
            <param name="value">Receives the parsed value</param>
            <param name="bytesConsumed">On a successful parse, receives the length in bytes of the substring that was parsed </param>
            <param name="standardFormat">Expected format of the Utf8 string</param>
            <returns>
            true for success. "bytesConsumed" contains the length in bytes of the substring that was parsed.
            false if the string was not syntactically valid or an overflow or underflow occurred. "bytesConsumed" is set to 0. 
            </returns>
            <remarks>
            Formats supported:
                c/t/T (default) [-][d.]hh:mm:ss[.fffffff]             (constant format)
                G               [-]d:hh:mm:ss.fffffff                 (general long)
                g               [-][d:]h:mm:ss[.f[f[f[f[f[f[f[]]]]]]] (general short)
            </remarks>
            <exceptions>
            <cref>System.FormatException</cref> if the format is not valid for this data type.
            </exceptions>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryParseTimeSpanFraction(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)">
            <summary>
            Parse the fraction portion of a TimeSpan. Must be 1..7 digits. If fewer than 7, zeroes are implied to the right. If more than 7, the TimeSpan
            parser rejects the string (even if the extra digits are all zeroes.)
            </summary>
        </member>
        <member name="M:System.Buffers.Text.Utf8Parser.TryCreateTimeSpan(System.Boolean,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.TimeSpan@)">
            <summary>
            Overflow-safe TryCreateTimeSpan
            </summary>
        </member>
        <member name="T:System.Memory`1">
            <summary>
            Memory represents a contiguous region of arbitrary memory similar to <see cref="T:System.Span`1"/>.
            Unlike <see cref="T:System.Span`1"/>, it is not a byref-like type.
            </summary>
        </member>
        <member name="M:System.Memory`1.#ctor(`0[])">
            <summary>
            Creates a new memory over the entirety of the target array.
            </summary>
            <param name="array">The target array.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
        </member>
        <member name="M:System.Memory`1.#ctor(`0[],System.Int32,System.Int32)">
            <summary>
            Creates a new memory over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.Memory`1.#ctor(System.Buffers.MemoryManager{`0},System.Int32)">
            <summary>
            Creates a new memory from a memory manager that provides specific method implementations beginning
            at 0 index and ending at 'end' index (exclusive).
            </summary>
            <param name="manager">The memory manager.</param>
            <param name="length">The number of items in the memory.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="length"/> is negative.
            </exception>
            <remarks>For internal infrastructure only</remarks>
        </member>
        <member name="M:System.Memory`1.#ctor(System.Buffers.MemoryManager{`0},System.Int32,System.Int32)">
            <summary>
            Creates a new memory from a memory manager that provides specific method implementations beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="manager">The memory manager.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or <paramref name="length"/> is negative.
            </exception>
            <remarks>For internal infrastructure only</remarks>
        </member>
        <member name="M:System.Memory`1.op_Implicit(`0[])~System.Memory{`0}">
            <summary>
            Defines an implicit conversion of an array to a <see cref="T:System.Memory`1"/>
            </summary>
        </member>
        <member name="M:System.Memory`1.op_Implicit(System.ArraySegment{`0})~System.Memory{`0}">
            <summary>
            Defines an implicit conversion of a <see cref="T:System.ArraySegment`1"/> to a <see cref="T:System.Memory`1"/>
            </summary>
        </member>
        <member name="M:System.Memory`1.op_Implicit(System.Memory{`0})~System.ReadOnlyMemory{`0}">
            <summary>
            Defines an implicit conversion of a <see cref="T:System.Memory`1"/> to a <see cref="T:System.ReadOnlyMemory`1"/>
            </summary>
        </member>
        <member name="P:System.Memory`1.Empty">
            <summary>
            Returns an empty <see cref="T:System.Memory`1"/>
            </summary>
        </member>
        <member name="P:System.Memory`1.Length">
            <summary>
            The number of items in the memory.
            </summary>
        </member>
        <member name="P:System.Memory`1.IsEmpty">
            <summary>
            Returns true if Length is 0.
            </summary>
        </member>
        <member name="M:System.Memory`1.ToString">
            <summary>
            For <see cref="T:System.Memory`1"/>, returns a new instance of string that represents the characters pointed to by the memory.
            Otherwise, returns a <see cref="T:System.String"/> with the name of the type and the number of elements.
            </summary>
        </member>
        <member name="M:System.Memory`1.Slice(System.Int32)">
            <summary>
            Forms a slice out of the given memory, beginning at 'start'.
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.Memory`1.Slice(System.Int32,System.Int32)">
            <summary>
            Forms a slice out of the given memory, beginning at 'start', of given length
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The desired length for the slice (exclusive).</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="P:System.Memory`1.Span">
            <summary>
            Returns a span from the memory.
            </summary>
        </member>
        <member name="M:System.Memory`1.CopyTo(System.Memory{`0})">
             <summary>
             Copies the contents of the memory into the destination. If the source
             and destination overlap, this method behaves as if the original values are in
             a temporary location before the destination is overwritten.
            
             <param name="destination">The Memory to copy items into.</param>
             <exception cref="T:System.ArgumentException">
             Thrown when the destination is shorter than the source.
             </exception>
             </summary>
        </member>
        <member name="M:System.Memory`1.TryCopyTo(System.Memory{`0})">
             <summary>
             Copies the contents of the memory into the destination. If the source
             and destination overlap, this method behaves as if the original values are in
             a temporary location before the destination is overwritten.
            
             <returns>If the destination is shorter than the source, this method
             return false and no data is written to the destination.</returns>
             </summary>
             <param name="destination">The span to copy items into.</param>
        </member>
        <member name="M:System.Memory`1.Pin">
            <summary>
            Creates a handle for the memory.
            The GC will not move the memory until the returned <see cref="T:System.Buffers.MemoryHandle"/>
            is disposed, enabling taking and using the memory's address.
            <exception cref="T:System.ArgumentException">
            An instance with nonprimitive (non-blittable) members cannot be pinned.
            </exception>
            </summary>
        </member>
        <member name="M:System.Memory`1.ToArray">
            <summary>
            Copies the contents from the memory into a new array.  This heap
            allocates, so should generally be avoided, however it is sometimes
            necessary to bridge the gap with APIs written in terms of arrays.
            </summary>
        </member>
        <member name="M:System.Memory`1.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            Returns true if the object is Memory or ReadOnlyMemory and if both objects point to the same array and have the same length.
            </summary>
        </member>
        <member name="M:System.Memory`1.Equals(System.Memory{`0})">
            <summary>
            Returns true if the memory points to the same array and has the same length.  Note that
            this does *not* check to see if the *contents* are equal.
            </summary>
        </member>
        <member name="M:System.Memory`1.GetHashCode">
            <summary>
            Serves as the default hash function.
            </summary>
        </member>
        <member name="T:System.MemoryExtensions">
            <summary>
            Extension methods for Span{T}, Memory{T}, and friends.
            </summary>
            <summary>
            Extension methods for Span{T}, Memory{T}, and friends.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Trim(System.ReadOnlySpan{System.Char})">
            <summary>
            Removes all leading and trailing white-space characters from the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlySpan{System.Char})">
            <summary>
            Removes all leading white-space characters from the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlySpan{System.Char})">
            <summary>
            Removes all trailing white-space characters from the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Trim(System.ReadOnlySpan{System.Char},System.Char)">
            <summary>
            Removes all leading and trailing occurrences of a specified character.
            </summary>
            <param name="span">The source span from which the character is removed.</param>
            <param name="trimChar">The specified character to look for and remove.</param>
        </member>
        <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlySpan{System.Char},System.Char)">
            <summary>
            Removes all leading occurrences of a specified character.
            </summary>
            <param name="span">The source span from which the character is removed.</param>
            <param name="trimChar">The specified character to look for and remove.</param>
        </member>
        <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlySpan{System.Char},System.Char)">
            <summary>
            Removes all trailing occurrences of a specified character.
            </summary>
            <param name="span">The source span from which the character is removed.</param>
            <param name="trimChar">The specified character to look for and remove.</param>
        </member>
        <member name="M:System.MemoryExtensions.Trim(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
            <summary>
            Removes all leading and trailing occurrences of a set of characters specified 
            in a readonly span from the span.
            </summary>
            <param name="span">The source span from which the characters are removed.</param>
            <param name="trimChars">The span which contains the set of characters to remove.</param>
            <remarks>If <paramref name="trimChars"/> is empty, white-space characters are removed instead.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
            <summary>
            Removes all leading occurrences of a set of characters specified 
            in a readonly span from the span.
            </summary>
            <param name="span">The source span from which the characters are removed.</param>
            <param name="trimChars">The span which contains the set of characters to remove.</param>
            <remarks>If <paramref name="trimChars"/> is empty, white-space characters are removed instead.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
            <summary>
            Removes all trailing occurrences of a set of characters specified 
            in a readonly span from the span.
            </summary>
            <param name="span">The source span from which the characters are removed.</param>
            <param name="trimChars">The span which contains the set of characters to remove.</param>
            <remarks>If <paramref name="trimChars"/> is empty, white-space characters are removed instead.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.IsWhiteSpace(System.ReadOnlySpan{System.Char})">
            <summary>
            Indicates whether the specified span contains only white-space characters.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.IndexOf``1(System.Span{``0},``0)">
            <summary>
            Searches for the specified value and returns the index of its first occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The value to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOf``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the specified sequence and returns the index of its first occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The sequence to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOf``1(System.Span{``0},``0)">
            <summary>
            Searches for the specified value and returns the index of its last occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The value to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOf``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the specified sequence and returns the index of its last occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The sequence to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.SequenceEqual``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether two sequences are equal by comparing the elements using IEquatable{T}.Equals(T). 
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.SequenceCompareTo``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines the relative order of the sequences being compared by comparing the elements using IComparable{T}.CompareTo(T). 
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.IndexOf``1(System.ReadOnlySpan{``0},``0)">
            <summary>
            Searches for the specified value and returns the index of its first occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The value to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOf``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the specified sequence and returns the index of its first occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The sequence to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOf``1(System.ReadOnlySpan{``0},``0)">
            <summary>
            Searches for the specified value and returns the index of its last occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The value to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOf``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the specified sequence and returns the index of its last occurrence. If not found, returns -1. Values are compared using IEquatable{T}.Equals(T). 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value">The sequence to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOfAny``1(System.Span{``0},``0,``0)">
            <summary>
            Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator. If not found, returns -1.
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOfAny``1(System.Span{``0},``0,``0,``0)">
            <summary>
            Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator. If not found, returns -1.
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
            <param name="value2">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOfAny``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator. If not found, returns -1. 
            </summary>
            <param name="span">The span to search.</param>
            <param name="values">The set of values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOfAny``1(System.ReadOnlySpan{``0},``0,``0)">
            <summary>
            Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator. If not found, returns -1.
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOfAny``1(System.ReadOnlySpan{``0},``0,``0,``0)">
            <summary>
            Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator. If not found, returns -1. 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
            <param name="value2">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.IndexOfAny``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator. If not found, returns -1. 
            </summary>
            <param name="span">The span to search.</param>
            <param name="values">The set of values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.Span{``0},``0,``0)">
            <summary>
            Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator. If not found, returns -1.
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.Span{``0},``0,``0,``0)">
            <summary>
            Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator. If not found, returns -1.
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
            <param name="value2">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator. If not found, returns -1. 
            </summary>
            <param name="span">The span to search.</param>
            <param name="values">The set of values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.ReadOnlySpan{``0},``0,``0)">
            <summary>
            Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator. If not found, returns -1.
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.ReadOnlySpan{``0},``0,``0,``0)">
            <summary>
            Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator. If not found, returns -1. 
            </summary>
            <param name="span">The span to search.</param>
            <param name="value0">One of the values to search for.</param>
            <param name="value1">One of the values to search for.</param>
            <param name="value2">One of the values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator. If not found, returns -1. 
            </summary>
            <param name="span">The span to search.</param>
            <param name="values">The set of values to search for.</param>
        </member>
        <member name="M:System.MemoryExtensions.SequenceEqual``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether two sequences are equal by comparing the elements using IEquatable{T}.Equals(T). 
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.SequenceCompareTo``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines the relative order of the sequences being compared by comparing the elements using IComparable{T}.CompareTo(T). 
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.StartsWith``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether the specified sequence appears at the start of the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.StartsWith``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether the specified sequence appears at the start of the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.EndsWith``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether the specified sequence appears at the end of the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.EndsWith``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether the specified sequence appears at the end of the span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Reverse``1(System.Span{``0})">
            <summary>
            Reverses the sequence of the elements in the entire span.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan``1(``0[])">
            <summary>
            Creates a new span over the target array.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan``1(``0[],System.Int32,System.Int32)">
            <summary>
            Creates a new Span over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the Span.</param>
            <param name="length">The number of items in the Span.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0})">
            <summary>
            Creates a new span over the portion of the target array segment.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0},System.Int32)">
            <summary>
            Creates a new Span over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="segment">The target array.</param>
            <param name="start">The index at which to begin the Span.</param>
            <remarks>Returns default when <paramref name="segment"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="segment"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=segment.Count).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0},System.Int32,System.Int32)">
            <summary>
            Creates a new Span over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="segment">The target array.</param>
            <param name="start">The index at which to begin the Span.</param>
            <param name="length">The number of items in the Span.</param>
            <remarks>Returns default when <paramref name="segment"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="segment"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=segment.Count).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory``1(``0[])">
            <summary>
            Creates a new memory over the target array.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory``1(``0[],System.Int32)">
            <summary>
            Creates a new memory over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=array.Length).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory``1(``0[],System.Int32,System.Int32)">
            <summary>
            Creates a new memory over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory``1(System.ArraySegment{``0})">
            <summary>
            Creates a new memory over the portion of the target array.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory``1(System.ArraySegment{``0},System.Int32)">
            <summary>
            Creates a new memory over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="segment">The target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <remarks>Returns default when <paramref name="segment"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="segment"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=segment.Count).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory``1(System.ArraySegment{``0},System.Int32,System.Int32)">
            <summary>
            Creates a new memory over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="segment">The target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
            <remarks>Returns default when <paramref name="segment"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="segment"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=segment.Count).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.CopyTo``1(``0[],System.Span{``0})">
             <summary>
             Copies the contents of the array into the span. If the source
             and destinations overlap, this method behaves as if the original values in
             a temporary location before the destination is overwritten.
             
            <param name="source">The array to copy items from.</param>
             <param name="destination">The span to copy items into.</param>
             <exception cref="T:System.ArgumentException">
             Thrown when the destination Span is shorter than the source array.
             </exception>
             </summary>
        </member>
        <member name="M:System.MemoryExtensions.CopyTo``1(``0[],System.Memory{``0})">
             <summary>
             Copies the contents of the array into the memory. If the source
             and destinations overlap, this method behaves as if the original values are in
             a temporary location before the destination is overwritten.
             
            <param name="source">The array to copy items from.</param>
             <param name="destination">The memory to copy items into.</param>
             <exception cref="T:System.ArgumentException">
             Thrown when the destination is shorter than the source array.
             </exception>
             </summary>
        </member>
        <member name="M:System.MemoryExtensions.Overlaps``1(System.Span{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether two sequences overlap in memory.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Overlaps``1(System.Span{``0},System.ReadOnlySpan{``0},System.Int32@)">
            <summary>
            Determines whether two sequences overlap in memory and outputs the element offset.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Overlaps``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
            <summary>
            Determines whether two sequences overlap in memory.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Overlaps``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0},System.Int32@)">
            <summary>
            Determines whether two sequences overlap in memory and outputs the element offset.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.BinarySearch``1(System.Span{``0},System.IComparable{``0})">
            <summary>
            Searches an entire sorted <see cref="T:System.Span`1"/> for a value
            using the specified <see cref="T:System.IComparable`1"/> generic interface.
            </summary>
            <typeparam name="T">The element type of the span.</typeparam>
            <param name="span">The sorted <see cref="T:System.Span`1"/> to search.</param>
            <param name="comparable">The <see cref="T:System.IComparable`1"/> to use when comparing.</param>
            <returns>
            The zero-based index of <paramref name="comparable"/> in the sorted <paramref name="span"/>,
            if <paramref name="comparable"/> is found; otherwise, a negative number that is the bitwise complement
            of the index of the next element that is larger than <paramref name="comparable"/> or, if there is
            no larger element, the bitwise complement of <see cref="P:System.Span`1.Length"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name = "comparable" /> is <see langword="null"/> .
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.BinarySearch``2(System.Span{``0},``1)">
            <summary>
            Searches an entire sorted <see cref="T:System.Span`1"/> for a value
            using the specified <typeparamref name="TComparable"/> generic type.
            </summary>
            <typeparam name="T">The element type of the span.</typeparam>
            <typeparam name="TComparable">The specific type of <see cref="T:System.IComparable`1"/>.</typeparam>
            <param name="span">The sorted <see cref="T:System.Span`1"/> to search.</param>
            <param name="comparable">The <typeparamref name="TComparable"/> to use when comparing.</param>
            <returns>
            The zero-based index of <paramref name="comparable"/> in the sorted <paramref name="span"/>,
            if <paramref name="comparable"/> is found; otherwise, a negative number that is the bitwise complement
            of the index of the next element that is larger than <paramref name="comparable"/> or, if there is
            no larger element, the bitwise complement of <see cref="P:System.Span`1.Length"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name = "comparable" /> is <see langword="null"/> .
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.BinarySearch``2(System.Span{``0},``0,``1)">
            <summary>
            Searches an entire sorted <see cref="T:System.Span`1"/> for the specified <paramref name="value"/>
            using the specified <typeparamref name="TComparer"/> generic type.
            </summary>
            <typeparam name="T">The element type of the span.</typeparam>
            <typeparam name="TComparer">The specific type of <see cref="T:System.Collections.Generic.IComparer`1"/>.</typeparam>
            <param name="span">The sorted <see cref="T:System.Span`1"/> to search.</param>
            <param name="value">The object to locate. The value can be null for reference types.</param>
            <param name="comparer">The <typeparamref name="TComparer"/> to use when comparing.</param>
            /// <returns>
            The zero-based index of <paramref name="value"/> in the sorted <paramref name="span"/>,
            if <paramref name="value"/> is found; otherwise, a negative number that is the bitwise complement
            of the index of the next element that is larger than <paramref name="value"/> or, if there is
            no larger element, the bitwise complement of <see cref="P:System.Span`1.Length"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name = "comparer" /> is <see langword="null"/> .
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.BinarySearch``1(System.ReadOnlySpan{``0},System.IComparable{``0})">
            <summary>
            Searches an entire sorted <see cref="T:System.ReadOnlySpan`1"/> for a value
            using the specified <see cref="T:System.IComparable`1"/> generic interface.
            </summary>
            <typeparam name="T">The element type of the span.</typeparam>
            <param name="span">The sorted <see cref="T:System.ReadOnlySpan`1"/> to search.</param>
            <param name="comparable">The <see cref="T:System.IComparable`1"/> to use when comparing.</param>
            <returns>
            The zero-based index of <paramref name="comparable"/> in the sorted <paramref name="span"/>,
            if <paramref name="comparable"/> is found; otherwise, a negative number that is the bitwise complement
            of the index of the next element that is larger than <paramref name="comparable"/> or, if there is
            no larger element, the bitwise complement of <see cref="P:System.ReadOnlySpan`1.Length"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name = "comparable" /> is <see langword="null"/> .
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.BinarySearch``2(System.ReadOnlySpan{``0},``1)">
            <summary>
            Searches an entire sorted <see cref="T:System.ReadOnlySpan`1"/> for a value
            using the specified <typeparamref name="TComparable"/> generic type.
            </summary>
            <typeparam name="T">The element type of the span.</typeparam>
            <typeparam name="TComparable">The specific type of <see cref="T:System.IComparable`1"/>.</typeparam>
            <param name="span">The sorted <see cref="T:System.ReadOnlySpan`1"/> to search.</param>
            <param name="comparable">The <typeparamref name="TComparable"/> to use when comparing.</param>
            <returns>
            The zero-based index of <paramref name="comparable"/> in the sorted <paramref name="span"/>,
            if <paramref name="comparable"/> is found; otherwise, a negative number that is the bitwise complement
            of the index of the next element that is larger than <paramref name="comparable"/> or, if there is
            no larger element, the bitwise complement of <see cref="P:System.ReadOnlySpan`1.Length"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name = "comparable" /> is <see langword="null"/> .
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.BinarySearch``2(System.ReadOnlySpan{``0},``0,``1)">
            <summary>
            Searches an entire sorted <see cref="T:System.ReadOnlySpan`1"/> for the specified <paramref name="value"/>
            using the specified <typeparamref name="TComparer"/> generic type.
            </summary>
            <typeparam name="T">The element type of the span.</typeparam>
            <typeparam name="TComparer">The specific type of <see cref="T:System.Collections.Generic.IComparer`1"/>.</typeparam>
            <param name="span">The sorted <see cref="T:System.ReadOnlySpan`1"/> to search.</param>
            <param name="value">The object to locate. The value can be null for reference types.</param>
            <param name="comparer">The <typeparamref name="TComparer"/> to use when comparing.</param>
            /// <returns>
            The zero-based index of <paramref name="value"/> in the sorted <paramref name="span"/>,
            if <paramref name="value"/> is found; otherwise, a negative number that is the bitwise complement
            of the index of the next element that is larger than <paramref name="value"/> or, if there is
            no larger element, the bitwise complement of <see cref="P:System.ReadOnlySpan`1.Length"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name = "comparer" /> is <see langword="null"/> .
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan``1(``0[],System.Int32)">
            <summary>
            Creates a new span over the portion of the target array.
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Contains(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
            <summary>
            Returns a value indicating whether the specified <paramref name="value"/> occurs within the <paramref name="span"/>.
            <param name="span">The source span.</param>
            <param name="value">The value to seek within the source span.</param>
            <param name="comparisonType">One of the enumeration values that determines how the <paramref name="span"/> and <paramref name="value"/> are compared.</param>
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.Equals(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
            <summary>
            Determines whether this <paramref name="span"/> and the specified <paramref name="other"/> span have the same characters
            when compared using the specified <paramref name="comparisonType"/> option.
            <param name="span">The source span.</param>
            <param name="other">The value to compare with the source span.</param>
            <param name="comparisonType">One of the enumeration values that determines how the <paramref name="span"/> and <paramref name="other"/> are compared.</param>
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.CompareTo(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
            <summary>
            Compares the specified <paramref name="span"/> and <paramref name="other"/> using the specified <paramref name="comparisonType"/>,
            and returns an integer that indicates their relative position in the sort order.
            <param name="span">The source span.</param>
            <param name="other">The value to compare with the source span.</param>
            <param name="comparisonType">One of the enumeration values that determines how the <paramref name="span"/> and <paramref name="other"/> are compared.</param>
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.IndexOf(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
            <summary>
            Reports the zero-based index of the first occurrence of the specified <paramref name="value"/> in the current <paramref name="span"/>.
            <param name="span">The source span.</param>
            <param name="value">The value to seek within the source span.</param>
            <param name="comparisonType">One of the enumeration values that determines how the <paramref name="span"/> and <paramref name="value"/> are compared.</param>
            </summary>
        </member>
        <member name="M:System.MemoryExtensions.ToLower(System.ReadOnlySpan{System.Char},System.Span{System.Char},System.Globalization.CultureInfo)">
            <summary>
            Copies the characters from the source span into the destination, converting each character to lowercase,
            using the casing rules of the specified culture.
            </summary>
            <param name="source">The source span.</param>
            <param name="destination">The destination span which contains the transformed characters.</param>
            <param name="culture">An object that supplies culture-specific casing rules.</param>
            <remarks>If the source and destinations overlap, this method behaves as if the original values are in
            a temporary location before the destination is overwritten.</remarks>
            <exception cref="T:System.ArgumentNullException">
            Thrown when <paramref name="culture"/> is null.
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.ToLowerInvariant(System.ReadOnlySpan{System.Char},System.Span{System.Char})">
            <summary>
            Copies the characters from the source span into the destination, converting each character to lowercase,
            using the casing rules of the invariant culture.
            </summary>
            <param name="source">The source span.</param>
            <param name="destination">The destination span which contains the transformed characters.</param>
            <remarks>If the source and destinations overlap, this method behaves as if the original values are in
            a temporary location before the destination is overwritten.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.ToUpper(System.ReadOnlySpan{System.Char},System.Span{System.Char},System.Globalization.CultureInfo)">
            <summary>
            Copies the characters from the source span into the destination, converting each character to uppercase,
            using the casing rules of the specified culture.
            </summary>
            <param name="source">The source span.</param>
            <param name="destination">The destination span which contains the transformed characters.</param>
            <param name="culture">An object that supplies culture-specific casing rules.</param>
            <remarks>If the source and destinations overlap, this method behaves as if the original values are in
            a temporary location before the destination is overwritten.</remarks>
            <exception cref="T:System.ArgumentNullException">
            Thrown when <paramref name="culture"/> is null.
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.ToUpperInvariant(System.ReadOnlySpan{System.Char},System.Span{System.Char})">
            <summary>
            Copies the characters from the source span into the destination, converting each character to uppercase
            using the casing rules of the invariant culture.
            </summary>
            <param name="source">The source span.</param>
            <param name="destination">The destination span which contains the transformed characters.</param>
            <remarks>If the source and destinations overlap, this method behaves as if the original values are in
            a temporary location before the destination is overwritten.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.EndsWith(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
            <summary>
            Determines whether the end of the <paramref name="span"/> matches the specified <paramref name="value"/> when compared using the specified <paramref name="comparisonType"/> option.
            </summary>
            <param name="span">The source span.</param>
            <param name="value">The sequence to compare to the end of the source span.</param>
            <param name="comparisonType">One of the enumeration values that determines how the <paramref name="span"/> and <paramref name="value"/> are compared.</param>
        </member>
        <member name="M:System.MemoryExtensions.StartsWith(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
            <summary>
            Determines whether the beginning of the <paramref name="span"/> matches the specified <paramref name="value"/> when compared using the specified <paramref name="comparisonType"/> option.
            </summary>
            <param name="span">The source span.</param>
            <param name="value">The sequence to compare to the beginning of the source span.</param>
            <param name="comparisonType">One of the enumeration values that determines how the <paramref name="span"/> and <paramref name="value"/> are compared.</param>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan(System.String)">
            <summary>
            Creates a new readonly span over the portion of the target string.
            </summary>
            <param name="text">The target string.</param>
            <remarks>Returns default when <paramref name="text"/> is null.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan(System.String,System.Int32)">
            <summary>
            Creates a new readonly span over the portion of the target string.
            </summary>
            <param name="text">The target string.</param>
            <param name="start">The index at which to begin this slice.</param>
            <remarks>Returns default when <paramref name="text"/> is null.</remarks>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index is not in range (&lt;0 or &gt;text.Length).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsSpan(System.String,System.Int32,System.Int32)">
            <summary>
            Creates a new readonly span over the portion of the target string.
            </summary>
            <param name="text">The target string.</param>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The desired length for the slice (exclusive).</param>
            <remarks>Returns default when <paramref name="text"/> is null.</remarks>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index or <paramref name="length"/> is not in range.
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory(System.String)">
            <summary>Creates a new <see cref="T:System.ReadOnlyMemory`1"/> over the portion of the target string.</summary>
            <param name="text">The target string.</param>
            <remarks>Returns default when <paramref name="text"/> is null.</remarks>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory(System.String,System.Int32)">
            <summary>Creates a new <see cref="T:System.ReadOnlyMemory`1"/> over the portion of the target string.</summary>
            <param name="text">The target string.</param>
            <param name="start">The index at which to begin this slice.</param>
            <remarks>Returns default when <paramref name="text"/> is null.</remarks>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index is not in range (&lt;0 or &gt;text.Length).
            </exception>
        </member>
        <member name="M:System.MemoryExtensions.AsMemory(System.String,System.Int32,System.Int32)">
            <summary>Creates a new <see cref="T:System.ReadOnlyMemory`1"/> over the portion of the target string.</summary>
            <param name="text">The target string.</param>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The desired length for the slice (exclusive).</param>
            <remarks>Returns default when <paramref name="text"/> is null.</remarks>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index or <paramref name="length"/> is not in range.
            </exception>
        </member>
        <member name="T:System.ReadOnlyMemory`1">
            <summary>
            Represents a contiguous region of memory, similar to <see cref="T:System.ReadOnlySpan`1"/>.
            Unlike <see cref="T:System.ReadOnlySpan`1"/>, it is not a byref-like type.
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.#ctor(`0[])">
            <summary>
            Creates a new memory over the entirety of the target array.
            </summary>
            <param name="array">The target array.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
        </member>
        <member name="M:System.ReadOnlyMemory`1.#ctor(`0[],System.Int32,System.Int32)">
            <summary>
            Creates a new memory over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.ReadOnlyMemory`1.#ctor(System.Object,System.Int32,System.Int32)">
            <summary>Creates a new memory over the existing object, start, and length. No validation is performed.</summary>
            <param name="obj">The target object.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
        </member>
        <member name="M:System.ReadOnlyMemory`1.op_Implicit(`0[])~System.ReadOnlyMemory{`0}">
            <summary>
            Defines an implicit conversion of an array to a <see cref="T:System.ReadOnlyMemory`1"/>
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.op_Implicit(System.ArraySegment{`0})~System.ReadOnlyMemory{`0}">
            <summary>
            Defines an implicit conversion of a <see cref="T:System.ArraySegment`1"/> to a <see cref="T:System.ReadOnlyMemory`1"/>
            </summary>
        </member>
        <member name="P:System.ReadOnlyMemory`1.Empty">
            <summary>
            Returns an empty <see cref="T:System.ReadOnlyMemory`1"/>
            </summary>
        </member>
        <member name="P:System.ReadOnlyMemory`1.Length">
            <summary>
            The number of items in the memory.
            </summary>
        </member>
        <member name="P:System.ReadOnlyMemory`1.IsEmpty">
            <summary>
            Returns true if Length is 0.
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.ToString">
            <summary>
            For <see cref="T:System.ReadOnlyMemory`1"/>, returns a new instance of string that represents the characters pointed to by the memory.
            Otherwise, returns a <see cref="T:System.String"/> with the name of the type and the number of elements.
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.Slice(System.Int32)">
            <summary>
            Forms a slice out of the given memory, beginning at 'start'.
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.ReadOnlyMemory`1.Slice(System.Int32,System.Int32)">
            <summary>
            Forms a slice out of the given memory, beginning at 'start', of given length
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The desired length for the slice (exclusive).</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="P:System.ReadOnlyMemory`1.Span">
            <summary>
            Returns a span from the memory.
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.CopyTo(System.Memory{`0})">
             <summary>
             Copies the contents of the read-only memory into the destination. If the source
             and destination overlap, this method behaves as if the original values are in
             a temporary location before the destination is overwritten.
            
             <param name="destination">The Memory to copy items into.</param>
             <exception cref="T:System.ArgumentException">
             Thrown when the destination is shorter than the source.
             </exception>
             </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.TryCopyTo(System.Memory{`0})">
             <summary>
             Copies the contents of the readonly-only memory into the destination. If the source
             and destination overlap, this method behaves as if the original values are in
             a temporary location before the destination is overwritten.
            
             <returns>If the destination is shorter than the source, this method
             return false and no data is written to the destination.</returns>
             </summary>
             <param name="destination">The span to copy items into.</param>
        </member>
        <member name="M:System.ReadOnlyMemory`1.Pin">
            <summary>
            Creates a handle for the memory.
            The GC will not move the memory until the returned <see cref="T:System.Buffers.MemoryHandle"/>
            is disposed, enabling taking and using the memory's address.
            <exception cref="T:System.ArgumentException">
            An instance with nonprimitive (non-blittable) members cannot be pinned.
            </exception>
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.ToArray">
            <summary>
            Copies the contents from the memory into a new array.  This heap
            allocates, so should generally be avoided, however it is sometimes
            necessary to bridge the gap with APIs written in terms of arrays.
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.Equals(System.ReadOnlyMemory{`0})">
            <summary>
            Returns true if the memory points to the same array and has the same length.  Note that
            this does *not* check to see if the *contents* are equal.
            </summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.GetHashCode">
            <summary>Returns the hash code for this <see cref="T:System.ReadOnlyMemory`1"/></summary>
        </member>
        <member name="M:System.ReadOnlyMemory`1.GetObjectStartLength(System.Int32@,System.Int32@)">
            <summary>Gets the state of the memory as individual fields.</summary>
            <param name="start">The offset.</param>
            <param name="length">The count.</param>
            <returns>The object.</returns>
        </member>
        <member name="T:System.ReadOnlySpan`1">
            <summary>
            ReadOnlySpan represents a contiguous region of arbitrary memory. Unlike arrays, it can point to either managed
            or native memory, or to memory allocated on the stack. It is type- and memory-safe.
            </summary>
            <summary>
            ReadOnlySpan represents a contiguous region of arbitrary memory. Unlike arrays, it can point to either managed
            or native memory, or to memory allocated on the stack. It is type- and memory-safe.
            </summary>
        </member>
        <member name="P:System.ReadOnlySpan`1.Length">
            <summary>
            The number of items in the read-only span.
            </summary>
        </member>
        <member name="P:System.ReadOnlySpan`1.IsEmpty">
            <summary>
            Returns true if Length is 0.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.op_Inequality(System.ReadOnlySpan{`0},System.ReadOnlySpan{`0})">
            <summary>
            Returns false if left and right point at the same memory and have the same length.  Note that
            this does *not* check to see if the *contents* are equal.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.Equals(System.Object)">
            <summary>
            This method is not supported as spans cannot be boxed. To compare two spans, use operator==.
            <exception cref="T:System.NotSupportedException">
            Always thrown by this method.
            </exception>
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.GetHashCode">
            <summary>
            This method is not supported as spans cannot be boxed.
            <exception cref="T:System.NotSupportedException">
            Always thrown by this method.
            </exception>
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.op_Implicit(`0[])~System.ReadOnlySpan{`0}">
            <summary>
            Defines an implicit conversion of an array to a <see cref="T:System.ReadOnlySpan`1"/>
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.op_Implicit(System.ArraySegment{`0})~System.ReadOnlySpan{`0}">
            <summary>
            Defines an implicit conversion of a <see cref="T:System.ArraySegment`1"/> to a <see cref="T:System.ReadOnlySpan`1"/>
            </summary>
        </member>
        <member name="P:System.ReadOnlySpan`1.Empty">
            <summary>
            Returns a 0-length read-only span whose base is the null pointer.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.GetEnumerator">
            <summary>Gets an enumerator for this span.</summary>
        </member>
        <member name="T:System.ReadOnlySpan`1.Enumerator">
            <summary>Enumerates the elements of a <see cref="T:System.ReadOnlySpan`1"/>.</summary>
        </member>
        <member name="F:System.ReadOnlySpan`1.Enumerator._span">
            <summary>The span being enumerated.</summary>
        </member>
        <member name="F:System.ReadOnlySpan`1.Enumerator._index">
            <summary>The next index to yield.</summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.Enumerator.#ctor(System.ReadOnlySpan{`0})">
            <summary>Initialize the enumerator.</summary>
            <param name="span">The span to enumerate.</param>
        </member>
        <member name="M:System.ReadOnlySpan`1.Enumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the span.</summary>
        </member>
        <member name="P:System.ReadOnlySpan`1.Enumerator.Current">
            <summary>Gets the element at the current position of the enumerator.</summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.#ctor(`0[])">
            <summary>
            Creates a new read-only span over the entirety of the target array.
            </summary>
            <param name="array">The target array.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
        </member>
        <member name="M:System.ReadOnlySpan`1.#ctor(`0[],System.Int32,System.Int32)">
            <summary>
            Creates a new read-only span over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the read-only span.</param>
            <param name="length">The number of items in the read-only span.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.ReadOnlySpan`1.#ctor(System.Void*,System.Int32)">
            <summary>
            Creates a new read-only span over the target unmanaged buffer.  Clearly this
            is quite dangerous, because we are creating arbitrarily typed T's
            out of a void*-typed block of memory.  And the length is not checked.
            But if this creation is correct, then all subsequent uses are correct.
            </summary>
            <param name="pointer">An unmanaged pointer to memory.</param>
            <param name="length">The number of <typeparamref name="T"/> elements the memory contains.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <typeparamref name="T"/> is reference type or contains pointers and hence cannot be stored in unmanaged memory.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="length"/> is negative.
            </exception>
        </member>
        <member name="P:System.ReadOnlySpan`1.Item(System.Int32)">
            <summary>
            Returns the specified element of the read-only span.
            </summary>
            <param name="index"></param>
            <returns></returns>
            <exception cref="T:System.IndexOutOfRangeException">
            Thrown when index less than 0 or index greater than or equal to Length
            </exception>
        </member>
        <member name="M:System.ReadOnlySpan`1.GetPinnableReference">
            <summary>
            Returns a reference to the 0th element of the Span. If the Span is empty, returns null reference.
            It can be used for pinning and is required to support the use of span within a fixed statement.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.CopyTo(System.Span{`0})">
             <summary>
             Copies the contents of this read-only span into destination span. If the source
             and destinations overlap, this method behaves as if the original values in
             a temporary location before the destination is overwritten.
            
             <param name="destination">The span to copy items into.</param>
             <exception cref="T:System.ArgumentException">
             Thrown when the destination Span is shorter than the source Span.
             </exception>
             </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.TryCopyTo(System.Span{`0})">
             <summary>
             Copies the contents of this read-only span into destination span. If the source
             and destinations overlap, this method behaves as if the original values in
             a temporary location before the destination is overwritten.
            
             <returns>If the destination span is shorter than the source span, this method
             return false and no data is written to the destination.</returns>
             </summary>
             <param name="destination">The span to copy items into.</param>
        </member>
        <member name="M:System.ReadOnlySpan`1.op_Equality(System.ReadOnlySpan{`0},System.ReadOnlySpan{`0})">
            <summary>
            Returns true if left and right point at the same memory and have the same length.  Note that
            this does *not* check to see if the *contents* are equal.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.ToString">
            <summary>
            For <see cref="T:System.Span`1"/>, returns a new instance of string that represents the characters pointed to by the span.
            Otherwise, returns a <see cref="T:System.String"/> with the name of the type and the number of elements.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.Slice(System.Int32)">
            <summary>
            Forms a slice out of the given read-only span, beginning at 'start'.
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.ReadOnlySpan`1.Slice(System.Int32,System.Int32)">
            <summary>
            Forms a slice out of the given read-only span, beginning at 'start', of given length
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The desired length for the slice (exclusive).</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.ReadOnlySpan`1.ToArray">
            <summary>
            Copies the contents of this read-only span into a new array.  This heap
            allocates, so should generally be avoided, however it is sometimes
            necessary to bridge the gap with APIs written in terms of arrays.
            </summary>
        </member>
        <member name="M:System.ReadOnlySpan`1.DangerousGetPinnableReference">
            <summary>
            This method is obsolete, use System.Runtime.InteropServices.MemoryMarshal.GetReference instead.
            Returns a reference to the 0th element of the Span. If the Span is empty, returns a reference to the location where the 0th element
            would have been stored. Such a reference can be used for pinning but must never be dereferenced.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.MemoryMarshal">
            <summary>
            Provides a collection of methods for interoperating with <see cref="T:System.Memory`1"/>, <see cref="T:System.ReadOnlyMemory`1"/>,
            <see cref="T:System.Span`1"/>, and <see cref="T:System.ReadOnlySpan`1"/>.
            </summary>
            <summary>
            Provides a collection of methods for interoperating with <see cref="T:System.Memory`1"/>, <see cref="T:System.ReadOnlyMemory`1"/>,
            <see cref="T:System.Span`1"/>, and <see cref="T:System.ReadOnlySpan`1"/>.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetArray``1(System.ReadOnlyMemory{``0},System.ArraySegment{``0}@)">
            <summary>
            Get an array segment from the underlying memory.
            If unable to get the array segment, return false with a default array segment.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetMemoryManager``2(System.ReadOnlyMemory{``0},``1@)">
            <summary>
            Gets an <see cref="T:System.Buffers.MemoryManager`1"/> from the underlying read-only memory.
            If unable to get the <typeparamref name="TManager"/> type, returns false.
            </summary>
            <typeparam name="T">The element type of the <paramref name="memory" />.</typeparam>
            <typeparam name="TManager">The type of <see cref="T:System.Buffers.MemoryManager`1"/> to try and retrive.</typeparam>
            <param name="memory">The memory to get the manager for.</param>
            <param name="manager">The returned manager of the <see cref="T:System.ReadOnlyMemory`1"/>.</param>
            <returns>A <see cref="T:System.Boolean"/> indicating if it was successful.</returns>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetMemoryManager``2(System.ReadOnlyMemory{``0},``1@,System.Int32@,System.Int32@)">
            <summary>
            Gets an <see cref="T:System.Buffers.MemoryManager`1"/> and <paramref name="start" />, <paramref name="length" /> from the underlying read-only memory.
            If unable to get the <typeparamref name="TManager"/> type, returns false.
            </summary>
            <typeparam name="T">The element type of the <paramref name="memory" />.</typeparam>
            <typeparam name="TManager">The type of <see cref="T:System.Buffers.MemoryManager`1"/> to try and retrive.</typeparam>
            <param name="memory">The memory to get the manager for.</param>
            <param name="manager">The returned manager of the <see cref="T:System.ReadOnlyMemory`1"/>.</param>
            <param name="start">The offset from the start of the <paramref name="manager" /> that the <paramref name="memory" /> represents.</param>
            <param name="length">The length of the <paramref name="manager" /> that the <paramref name="memory" /> represents.</param>
            <returns>A <see cref="T:System.Boolean"/> indicating if it was successful.</returns>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.ToEnumerable``1(System.ReadOnlyMemory{``0})">
            <summary>
            Creates an <see cref="T:System.Collections.Generic.IEnumerable`1"/> view of the given <paramref name="memory" /> to allow
            the <paramref name="memory" /> to be used in existing APIs that take an <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            </summary>
            <typeparam name="T">The element type of the <paramref name="memory" />.</typeparam>
            <param name="memory">The ReadOnlyMemory to view as an <see cref="T:System.Collections.Generic.IEnumerable`1"/></param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> view of the given <paramref name="memory" /></returns>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetString(System.ReadOnlyMemory{System.Char},System.String@,System.Int32@,System.Int32@)">
            <summary>Attempts to get the underlying <see cref="T:System.String"/> from a <see cref="T:System.ReadOnlyMemory`1"/>.</summary>
            <param name="memory">The memory that may be wrapping a <see cref="T:System.String"/> object.</param>
            <param name="text">The string.</param>
            <param name="start">The starting location in <paramref name="text"/>.</param>
            <param name="length">The number of items in <paramref name="text"/>.</param>
            <returns></returns>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.Read``1(System.ReadOnlySpan{System.Byte})">
            <summary>
            Reads a structure of type T out of a read-only span of bytes.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryRead``1(System.ReadOnlySpan{System.Byte},``0@)">
            <summary>
            Reads a structure of type T out of a span of bytes.
            <returns>If the span is too small to contain the type T, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.Write``1(System.Span{System.Byte},``0@)">
            <summary>
            Writes a structure of type T into a span of bytes.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryWrite``1(System.Span{System.Byte},``0@)">
            <summary>
            Writes a structure of type T into a span of bytes.
            <returns>If the span is too small to contain the type T, return false.</returns>
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.CreateFromPinnedArray``1(``0[],System.Int32,System.Int32)">
            <summary>
            Creates a new memory over the portion of the pre-pinned target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The pre-pinned target array.</param>
            <param name="start">The index at which to begin the memory.</param>
            <param name="length">The number of items in the memory.</param>
            <remarks>This method should only be called on an array that is already pinned and 
            that array should not be unpinned while the returned Memory<typeparamref name="T"/> is still in use.
            Calling this method on an unpinned array could result in memory corruption.</remarks>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsBytes``1(System.Span{``0})">
            <summary>
            Casts a Span of one primitive type <typeparamref name="T"/> to Span of bytes.
            That type may not contain pointers or references. This is checked at runtime in order to preserve type safety.
            </summary>
            <param name="span">The source slice, of type <typeparamref name="T"/>.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <typeparamref name="T"/> contains pointers.
            </exception>
            <exception cref="T:System.OverflowException">
            Thrown if the Length property of the new Span would exceed Int32.MaxValue.
            </exception>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsBytes``1(System.ReadOnlySpan{``0})">
            <summary>
            Casts a ReadOnlySpan of one primitive type <typeparamref name="T"/> to ReadOnlySpan of bytes.
            That type may not contain pointers or references. This is checked at runtime in order to preserve type safety.
            </summary>
            <param name="span">The source slice, of type <typeparamref name="T"/>.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <typeparamref name="T"/> contains pointers.
            </exception>
            <exception cref="T:System.OverflowException">
            Thrown if the Length property of the new Span would exceed Int32.MaxValue.
            </exception>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsMemory``1(System.ReadOnlyMemory{``0})">
            <summary>Creates a <see cref="T:System.Memory`1"/> from a <see cref="T:System.ReadOnlyMemory`1"/>.</summary>
            <param name="memory">The <see cref="T:System.ReadOnlyMemory`1"/>.</param>
            <returns>A <see cref="T:System.Memory`1"/> representing the same memory as the <see cref="T:System.ReadOnlyMemory`1"/>, but writable.</returns>
            <remarks>
            <see cref="M:System.Runtime.InteropServices.MemoryMarshal.AsMemory``1(System.ReadOnlyMemory{``0})"/> must be used with extreme caution.  <see cref="T:System.ReadOnlyMemory`1"/> is used
            to represent immutable data and other memory that is not meant to be written to; <see cref="T:System.Memory`1"/> instances created
            by <see cref="M:System.Runtime.InteropServices.MemoryMarshal.AsMemory``1(System.ReadOnlyMemory{``0})"/> should not be written to.  The method exists to enable variables typed
            as <see cref="T:System.Memory`1"/> but only used for reading to store a <see cref="T:System.ReadOnlyMemory`1"/>.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.GetReference``1(System.Span{``0})">
            <summary>
            Returns a reference to the 0th element of the Span. If the Span is empty, returns a reference to the location where the 0th element
            would have been stored. Such a reference can be used for pinning but must never be dereferenced.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.GetReference``1(System.ReadOnlySpan{``0})">
            <summary>
            Returns a reference to the 0th element of the ReadOnlySpan. If the Span is empty, returns a reference to the location where the 0th element
            would have been stored. Such a reference can be used for pinning but must never be dereferenced.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.Cast``2(System.Span{``0})">
            <summary>
            Casts a Span of one primitive type <typeparamref name="TFrom"/> to another primitive type <typeparamref name="TTo"/>.
            These types may not contain pointers or references. This is checked at runtime in order to preserve type safety.
            </summary>
            <remarks>
            Supported only for platforms that support misaligned memory access.
            </remarks>
            <param name="span">The source slice, of type <typeparamref name="TFrom"/>.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <typeparamref name="TFrom"/> or <typeparamref name="TTo"/> contains pointers.
            </exception>
        </member>
        <member name="M:System.Runtime.InteropServices.MemoryMarshal.Cast``2(System.ReadOnlySpan{``0})">
            <summary>
            Casts a ReadOnlySpan of one primitive type <typeparamref name="TFrom"/> to another primitive type <typeparamref name="TTo"/>.
            These types may not contain pointers or references. This is checked at runtime in order to preserve type safety.
            </summary>
            <remarks>
            Supported only for platforms that support misaligned memory access.
            </remarks>
            <param name="span">The source slice, of type <typeparamref name="TFrom"/>.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <typeparamref name="TFrom"/> or <typeparamref name="TTo"/> contains pointers.
            </exception>
        </member>
        <member name="T:System.Runtime.InteropServices.SequenceMarshal">
            <summary>
            Provides a collection of methods for interoperating with <see cref="T:System.Buffers.ReadOnlySequence`1"/>
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetReadOnlySequenceSegment``1(System.Buffers.ReadOnlySequence{``0},System.Buffers.ReadOnlySequenceSegment{``0}@,System.Int32@,System.Buffers.ReadOnlySequenceSegment{``0}@,System.Int32@)">
            <summary>
            Get <see cref="T:System.Buffers.ReadOnlySequenceSegment`1"/> from the underlying <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            If unable to get the <see cref="T:System.Buffers.ReadOnlySequenceSegment`1"/>, return false.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetArray``1(System.Buffers.ReadOnlySequence{``0},System.ArraySegment{``0}@)">
            <summary>
            Get an array segment from the underlying <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            If unable to get the array segment, return false with a default array segment.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetReadOnlyMemory``1(System.Buffers.ReadOnlySequence{``0},System.ReadOnlyMemory{``0}@)">
            <summary>
            Get <see cref="T:System.ReadOnlyMemory`1"/> from the underlying <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            If unable to get the <see cref="T:System.ReadOnlyMemory`1"/>, return false.
            </summary>
        </member>
        <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetString(System.Buffers.ReadOnlySequence{System.Char},System.String@,System.Int32@,System.Int32@)">
            <summary>
            Get <see cref="T:System.String"/> from the underlying <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            If unable to get the <see cref="T:System.String"/>, return false.
            </summary>
        </member>
        <member name="T:System.SequencePosition">
            <summary>
            Represents position in non-contiguous set of memory.
            Properties of this type should not be interpreted by anything but the type that created it.
            </summary>
        </member>
        <member name="M:System.SequencePosition.#ctor(System.Object,System.Int32)">
            <summary>
            Creates new <see cref="T:System.SequencePosition"/>
            </summary>
        </member>
        <member name="M:System.SequencePosition.GetObject">
            <summary>
            Returns object part of this <see cref="T:System.SequencePosition"/>
            </summary>
        </member>
        <member name="M:System.SequencePosition.GetInteger">
            <summary>
            Returns integer part of this <see cref="T:System.SequencePosition"/>
            </summary>
        </member>
        <member name="M:System.SequencePosition.Equals(System.SequencePosition)">
            <summary>
            Indicates whether the current <see cref="T:System.SequencePosition"/> is equal to another <see cref="T:System.SequencePosition"/>.
            <see cref="T:System.SequencePosition"/> equality does not guarantee that they point to the same location in <see cref="T:System.Buffers.ReadOnlySequence`1" />
            </summary>
        </member>
        <member name="M:System.SequencePosition.Equals(System.Object)">
            <summary>
            Indicates whether the current <see cref="T:System.SequencePosition"/> is equal to another <see cref="T:System.Object"/>.
            <see cref="T:System.SequencePosition"/> equality does not guarantee that they point to the same location in <see cref="T:System.Buffers.ReadOnlySequence`1" />
            </summary>
        </member>
        <member name="M:System.SequencePosition.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:System.Span`1">
            <summary>
            Span represents a contiguous region of arbitrary memory. Unlike arrays, it can point to either managed
            or native memory, or to memory allocated on the stack. It is type- and memory-safe.
            </summary>
            <summary>
            Span represents a contiguous region of arbitrary memory. Unlike arrays, it can point to either managed
            or native memory, or to memory allocated on the stack. It is type- and memory-safe.
            </summary>
        </member>
        <member name="P:System.Span`1.Length">
            <summary>
            The number of items in the span.
            </summary>
        </member>
        <member name="P:System.Span`1.IsEmpty">
            <summary>
            Returns true if Length is 0.
            </summary>
        </member>
        <member name="M:System.Span`1.op_Inequality(System.Span{`0},System.Span{`0})">
            <summary>
            Returns false if left and right point at the same memory and have the same length.  Note that
            this does *not* check to see if the *contents* are equal.
            </summary>
        </member>
        <member name="M:System.Span`1.Equals(System.Object)">
            <summary>
            This method is not supported as spans cannot be boxed. To compare two spans, use operator==.
            <exception cref="T:System.NotSupportedException">
            Always thrown by this method.
            </exception>
            </summary>
        </member>
        <member name="M:System.Span`1.GetHashCode">
            <summary>
            This method is not supported as spans cannot be boxed.
            <exception cref="T:System.NotSupportedException">
            Always thrown by this method.
            </exception>
            </summary>
        </member>
        <member name="M:System.Span`1.op_Implicit(`0[])~System.Span{`0}">
            <summary>
            Defines an implicit conversion of an array to a <see cref="T:System.Span`1"/>
            </summary>
        </member>
        <member name="M:System.Span`1.op_Implicit(System.ArraySegment{`0})~System.Span{`0}">
            <summary>
            Defines an implicit conversion of a <see cref="T:System.ArraySegment`1"/> to a <see cref="T:System.Span`1"/>
            </summary>
        </member>
        <member name="P:System.Span`1.Empty">
            <summary>
            Returns an empty <see cref="T:System.Span`1"/>
            </summary>
        </member>
        <member name="M:System.Span`1.GetEnumerator">
            <summary>Gets an enumerator for this span.</summary>
        </member>
        <member name="T:System.Span`1.Enumerator">
            <summary>Enumerates the elements of a <see cref="T:System.Span`1"/>.</summary>
        </member>
        <member name="F:System.Span`1.Enumerator._span">
            <summary>The span being enumerated.</summary>
        </member>
        <member name="F:System.Span`1.Enumerator._index">
            <summary>The next index to yield.</summary>
        </member>
        <member name="M:System.Span`1.Enumerator.#ctor(System.Span{`0})">
            <summary>Initialize the enumerator.</summary>
            <param name="span">The span to enumerate.</param>
        </member>
        <member name="M:System.Span`1.Enumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the span.</summary>
        </member>
        <member name="P:System.Span`1.Enumerator.Current">
            <summary>Gets the element at the current position of the enumerator.</summary>
        </member>
        <member name="M:System.Span`1.#ctor(`0[])">
            <summary>
            Creates a new span over the entirety of the target array.
            </summary>
            <param name="array">The target array.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
        </member>
        <member name="M:System.Span`1.#ctor(`0[],System.Int32,System.Int32)">
            <summary>
            Creates a new span over the portion of the target array beginning
            at 'start' index and ending at 'end' index (exclusive).
            </summary>
            <param name="array">The target array.</param>
            <param name="start">The index at which to begin the span.</param>
            <param name="length">The number of items in the span.</param>
            <remarks>Returns default when <paramref name="array"/> is null.</remarks>
            <exception cref="T:System.ArrayTypeMismatchException">Thrown when <paramref name="array"/> is covariant and array's type is not exactly T[].</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in the range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.Span`1.#ctor(System.Void*,System.Int32)">
            <summary>
            Creates a new span over the target unmanaged buffer.  Clearly this
            is quite dangerous, because we are creating arbitrarily typed T's
            out of a void*-typed block of memory.  And the length is not checked.
            But if this creation is correct, then all subsequent uses are correct.
            </summary>
            <param name="pointer">An unmanaged pointer to memory.</param>
            <param name="length">The number of <typeparamref name="T"/> elements the memory contains.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <typeparamref name="T"/> is reference type or contains pointers and hence cannot be stored in unmanaged memory.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="length"/> is negative.
            </exception>
        </member>
        <member name="P:System.Span`1.Item(System.Int32)">
            <summary>
            Returns a reference to specified element of the Span.
            </summary>
            <param name="index"></param>
            <returns></returns>
            <exception cref="T:System.IndexOutOfRangeException">
            Thrown when index less than 0 or index greater than or equal to Length
            </exception>
        </member>
        <member name="M:System.Span`1.GetPinnableReference">
            <summary>
            Returns a reference to the 0th element of the Span. If the Span is empty, returns null reference.
            It can be used for pinning and is required to support the use of span within a fixed statement.
            </summary>
        </member>
        <member name="M:System.Span`1.Clear">
            <summary>
            Clears the contents of this span.
            </summary>
        </member>
        <member name="M:System.Span`1.Fill(`0)">
            <summary>
            Fills the contents of this span with the given value.
            </summary>
        </member>
        <member name="M:System.Span`1.CopyTo(System.Span{`0})">
             <summary>
             Copies the contents of this span into destination span. If the source
             and destinations overlap, this method behaves as if the original values in
             a temporary location before the destination is overwritten.
            
             <param name="destination">The span to copy items into.</param>
             <exception cref="T:System.ArgumentException">
             Thrown when the destination Span is shorter than the source Span.
             </exception>
             </summary>
        </member>
        <member name="M:System.Span`1.TryCopyTo(System.Span{`0})">
             <summary>
             Copies the contents of this span into destination span. If the source
             and destinations overlap, this method behaves as if the original values in
             a temporary location before the destination is overwritten.
            
             <returns>If the destination span is shorter than the source span, this method
             return false and no data is written to the destination.</returns>
             </summary>
             <param name="destination">The span to copy items into.</param>
        </member>
        <member name="M:System.Span`1.op_Equality(System.Span{`0},System.Span{`0})">
            <summary>
            Returns true if left and right point at the same memory and have the same length.  Note that
            this does *not* check to see if the *contents* are equal.
            </summary>
        </member>
        <member name="M:System.Span`1.op_Implicit(System.Span{`0})~System.ReadOnlySpan{`0}">
            <summary>
            Defines an implicit conversion of a <see cref="T:System.Span`1"/> to a <see cref="T:System.ReadOnlySpan`1"/>
            </summary>
        </member>
        <member name="M:System.Span`1.ToString">
            <summary>
            For <see cref="T:System.Span`1"/>, returns a new instance of string that represents the characters pointed to by the span.
            Otherwise, returns a <see cref="T:System.String"/> with the name of the type and the number of elements.
            </summary>
        </member>
        <member name="M:System.Span`1.Slice(System.Int32)">
            <summary>
            Forms a slice out of the given span, beginning at 'start'.
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.Span`1.Slice(System.Int32,System.Int32)">
            <summary>
            Forms a slice out of the given span, beginning at 'start', of given length
            </summary>
            <param name="start">The index at which to begin this slice.</param>
            <param name="length">The desired length for the slice (exclusive).</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the specified <paramref name="start"/> or end index is not in range (&lt;0 or &gt;=Length).
            </exception>
        </member>
        <member name="M:System.Span`1.ToArray">
            <summary>
            Copies the contents of this span into a new array.  This heap
            allocates, so should generally be avoided, however it is sometimes
            necessary to bridge the gap with APIs written in terms of arrays.
            </summary>
        </member>
        <member name="M:System.Span`1.DangerousGetPinnableReference">
            <summary>
            This method is obsolete, use System.Runtime.InteropServices.MemoryMarshal.GetReference instead.
            Returns a reference to the 0th element of the Span. If the Span is empty, returns a reference to the location where the 0th element
            would have been stored. Such a reference can be used for pinning but must never be dereferenced.
            </summary>
        </member>
        <member name="M:System.SpanHelpers.CopyTo``1(``0@,System.Int32,``0@,System.Int32)">
             <summary>
             Implements the copy functionality used by Span and ReadOnlySpan.
            
             NOTE: Fast span implements TryCopyTo in corelib and therefore this implementation
                   is only used by portable span. The code must live in code that only compiles
                   for portable span which means either each individual span implementation
                   of this shared code file. Other shared SpanHelper.X.cs files are compiled
                   for both portable and fast span implementations.
             </summary>
        </member>
        <member name="M:System.SpanHelpers.Add``1(System.IntPtr,System.Int32)">
             <summary>
             Computes "start + index * sizeof(T)", using the unsigned IntPtr-sized multiplication for 32 and 64 bits.
            
             Assumptions:
                 Start and index are non-negative, and already pre-validated to be within the valid range of their containing Span.
            
                 If the byte length (Span.Length * sizeof(T)) does an unsigned overflow (i.e. the buffer wraps or is too big to fit within the address space),
                 the behavior is undefined.
            
             </summary>
        </member>
        <member name="M:System.SpanHelpers.IsReferenceOrContainsReferences``1">
            <summary>
            Determine if a type is eligible for storage in unmanaged memory.
            Portable equivalent of RuntimeHelpers.IsReferenceOrContainsReferences{T}()
            </summary>
        </member>
        <member name="P:System.SR.NotSupported_CannotCallEqualsOnSpan">
            <summary>Equals() on Span and ReadOnlySpan is not supported. Use operator== instead.</summary>
        </member>
        <member name="P:System.SR.NotSupported_CannotCallGetHashCodeOnSpan">
            <summary>GetHashCode() on Span and ReadOnlySpan is not supported.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidTypeWithPointersNotSupported">
            <summary>Cannot use type '{0}'. Only value types without pointers or references are supported.</summary>
        </member>
        <member name="P:System.SR.Argument_DestinationTooShort">
            <summary>Destination is too short.</summary>
        </member>
        <member name="P:System.SR.MemoryDisposed">
            <summary>Memory&lt;T&gt; has been disposed.</summary>
        </member>
        <member name="P:System.SR.OutstandingReferences">
            <summary>Release all references before disposing this instance.</summary>
        </member>
        <member name="P:System.SR.Argument_BadFormatSpecifier">
            <summary>Format specifier was invalid.</summary>
        </member>
        <member name="P:System.SR.Argument_GWithPrecisionNotSupported">
            <summary>The 'G' format combined with a precision is not supported.</summary>
        </member>
        <member name="P:System.SR.Argument_CannotParsePrecision">
            <summary>Characters following the format symbol must be a number of {0} or less.</summary>
        </member>
        <member name="P:System.SR.Argument_PrecisionTooLarge">
            <summary>Precision cannot be larger than {0}.</summary>
        </member>
        <member name="P:System.SR.Argument_OverlapAlignmentMismatch">
            <summary>Overlapping spans have mismatching alignment.</summary>
        </member>
        <member name="P:System.SR.EndPositionNotReached">
            <summary>End position was not reached during enumeration.</summary>
        </member>
        <member name="P:System.SR.UnexpectedSegmentType">
            <summary>Unexpected segment type.</summary>
        </member>
    </members>
</doc>
