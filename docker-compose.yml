# Removed version as it's obsolete in newer Docker Compose

services:
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: emaily-sqlserver
    hostname: emaily-sqlserver
    ports:
      - "${SQL_PORT:-1433}:1433"
    environment:
      # Required SQL Server environment variables
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${SA_PASSWORD:-YourStrong@Passw0rd}
      - MSSQL_PID=${MSSQL_PID:-Developer}
      
      # Performance and resource optimization
      - MSSQL_MEMORY_LIMIT_MB=${MSSQL_MEMORY_LIMIT_MB:-2048}
      - MSSQL_TCP_PORT=${MSSQL_TCP_PORT:-1433}
      - MSSQL_AGENT_ENABLED=${MSSQL_AGENT_ENABLED:-true}
      
      # Security and connection settings
      - MSSQL_ENABLE_HADR=${MSSQL_ENABLE_HADR:-0}
      - MSSQL_BACKUP_COMPRESSION_DEFAULT=${MSSQL_BACKUP_COMPRESSION_DEFAULT:-1}
      
      # Database initialization
      - ACCEPT_EULA=Y
    volumes:
      # Persistent data storage
      - sqlserver_data:/var/opt/mssql/data
      - sqlserver_log:/var/opt/mssql/log
      - sqlserver_backup:/var/opt/mssql/backup
      
      # Custom initialization scripts
      - ./docker/sqlserver/init:/docker-entrypoint-initdb.d:ro
      
      # Configuration files
      - ./docker/sqlserver/config:/opt/mssql/config:ro
    networks:
      - emaily-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: ${DOCKER_MEMORY_LIMIT:-4G}
          cpus: '${DOCKER_CPU_LIMIT:-2.0}'
        reservations:
          memory: ${DOCKER_MEMORY_RESERVATION:-1G}
          cpus: '${DOCKER_CPU_RESERVATION:-0.5}'
    healthcheck:
      test: [
        "CMD-SHELL", 
        "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P $$SA_PASSWORD -Q 'SELECT 1' -b -o /dev/null"
      ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true
    user: "10001"
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # Email Processor Service (optional - for development/testing)
  email-processor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: emaily-processor
    depends_on:
      sqlserver:
        condition: service_healthy
    environment:
      # Database connection settings aligned with appsettings.json
      - EmailProcessor__Database__ConnectionString=Server=sqlserver,1433;Database=${DATABASE_NAME:-EmailProcessor};User Id=sa;Password=${SA_PASSWORD:-YourStrong@Passw0rd};TrustServerCertificate=true;Max Pool Size=${MAX_POOL_SIZE:-100};Min Pool Size=${MIN_POOL_SIZE:-0};Connection Timeout=${CONNECTION_TIMEOUT:-15};Pooling=${ENABLE_POOLING:-true};
      
      # Storage configuration
      - EmailProcessor__Storage__LocalBasePath=${LOCAL_STORAGE_PATH:-/app/data/attachments}
      - EmailProcessor__Storage__CreateDirectories=true
      
      # Logging configuration
      - EmailProcessor__Logging__LogLevel=${LOG_LEVEL:-Information}
      - EmailProcessor__Logging__LogFilePath=/app/logs/EmailProcessor-.log
      - EmailProcessor__Logging__EnableConsoleLogging=true
      
      # Processing configuration
      - EmailProcessor__Processing__MaxConcurrentProcessing=${MAX_CONCURRENT:-10}
      - EmailProcessor__Processing__ProcessingTimeoutSeconds=${PROCESSING_TIMEOUT:-30}
      
      # Communication configuration
      - EmailProcessor__Communication__NamedPipeName=EmailProcessorPipe
      
      # ASP.NET Core environment
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
    volumes:
      - email_attachments:/app/data/attachments
      - email_logs:/app/logs
    networks:
      - emaily-network
    restart: unless-stopped
    profiles:
      - full-stack
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  sqlserver_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./docker/volumes/sqlserver/data}
  sqlserver_log:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-./docker/volumes/sqlserver/log}
  sqlserver_backup:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${BACKUP_PATH:-./docker/volumes/sqlserver/backup}
  email_attachments:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${ATTACHMENTS_PATH:-./docker/volumes/email/attachments}
  email_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${EMAIL_LOGS_PATH:-./docker/volumes/email/logs}

networks:
  emaily-network:
    driver: bridge
    ipam:
      config:
        - subnet: ${SUBNET:-**********/16}
    driver_opts:
      com.docker.network.bridge.name: emaily-bridge
      com.docker.network.driver.mtu: 1500