# UNC Storage Fallback Configuration

## Overview

The Email Attachment Processor has been enhanced to handle cases where UNC storage is not available or not properly configured. The application will no longer fail to start when UNC paths are invalid or inaccessible.

## Key Changes

### 1. NullStorageProvider
- A new `NullStorageProvider` class has been implemented that acts as a fallback when UNC storage is not available
- Implements the `IStorageProvider` interface but returns appropriate error responses
- Logs warnings when UNC storage operations are attempted but not available

### 2. Conditional UNC Storage Registration
- The UNC storage provider is now registered conditionally in the dependency injection container
- If the UNC path is invalid or the provider fails to initialize, a `NullStorageProvider` is used instead
- The application continues to function with local storage only

### 3. Enhanced DualStorageService
- Updated to handle `NullStorageProvider` gracefully
- Continues to work when only local storage is available
- Provides appropriate logging and error handling

### 4. Configuration Option
- Added `UncStorageRequired` configuration option to control whether UNC storage is mandatory

## Configuration

### Basic Configuration

In your `appsettings.json` or `appsettings.Development.json`:

```json
{
  "EmailProcessor": {
    "Storage": {
      "LocalBasePath": "C:\\EmailAttachments",
      "UncBasePath": "\\\\server\\share\\EmailAttachments",
      "UncUsername": "username",
      "UncPassword": "password",
      "UncStorageRequired": false
    }
  }
}
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `LocalBasePath` | string | `C:\EmailAttachments` | Base directory for local storage |
| `UncBasePath` | string | `\\server\share\EmailAttachments` | UNC path for network storage |
| `UncUsername` | string | null | Username for UNC authentication |
| `UncPassword` | string | null | Password for UNC authentication |
| `UncStorageRequired` | bool | false | Whether UNC storage is required for startup |

### UNC Storage Required Mode

If you want the application to fail fast when UNC storage is not available, set:

```json
{
  "EmailProcessor": {
    "Storage": {
      "UncStorageRequired": true
    }
  }
}
```

When `UncStorageRequired` is `true`:
- The application will throw an exception during startup if UNC storage cannot be initialized
- This is useful for environments where UNC storage is mandatory

When `UncStorageRequired` is `false` (default):
- The application will start with local storage only if UNC storage is not available
- UNC storage operations will be logged as warnings but won't cause failures
- The application continues to function normally

## Behavior

### When UNC Storage is Available
- Files are saved to both local and UNC storage
- Both storage locations are used for redundancy
- Health checks report both storage systems as available

### When UNC Storage is Not Available
- Files are saved to local storage only
- UNC storage operations are logged as warnings
- Health checks report UNC storage as unavailable
- The application continues to function normally

### Error Scenarios Handled
- Invalid UNC paths (not starting with `\\`)
- Network connectivity issues
- Authentication failures
- Missing or inaccessible network shares
- Configuration errors

## Logging

The application provides detailed logging for UNC storage issues:

- **Warning**: When UNC storage is not configured or invalid
- **Error**: When UNC storage initialization fails
- **Debug**: When UNC storage operations are skipped
- **Information**: When the application starts with local storage only

## Testing

Unit tests have been added to verify:
- UNC storage provider validation
- NullStorageProvider behavior
- DualStorageService fallback handling
- Configuration parsing

## Migration

No migration is required for existing installations. The changes are backward compatible:
- Existing configurations will continue to work
- The default behavior is to make UNC storage optional
- Applications will start successfully even with invalid UNC paths 