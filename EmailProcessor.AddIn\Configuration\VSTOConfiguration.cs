using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Win32;
using EmailProcessor.AddIn.Interfaces;
using System.IO;

namespace EmailProcessor.AddIn.Configuration
{
    /// <summary>
    /// VSTO configuration management using registry storage
    /// </summary>
    public class VSTOConfiguration : IVSTOConfiguration
    {
        private const string RegistryKeyPath = @"SOFTWARE\EmailProcessor\VSTO";
        private readonly Dictionary<string, string> _configuration;

        public string NamedPipeName { get; private set; } = "EmailProcessorPipe";
        public int ConnectionTimeoutSeconds { get; private set; } = 10;
        public long MaxFileSizeBytes { get; private set; } = 100 * 1024 * 1024; // 100MB
        public int RetryCount { get; private set; } = 3;
        public int RetryDelaySeconds { get; private set; } = 2;
        public bool ProcessReceivedEmails { get; private set; } = true;
        public bool ProcessSentEmails { get; private set; } = true;
        public List<string> ExcludedFileExtensions { get; private set; } = new List<string>();
        public string LogFilePath { get; private set; } = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "EmailProcessor", "VSTO.log");
        public string LogLevel { get; private set; } = "Information";

        public VSTOConfiguration()
        {
            _configuration = new Dictionary<string, string>();
            LoadConfiguration();
        }

        public void LoadConfiguration()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(RegistryKeyPath))
                {
                    if (key != null)
                    {
                        // Load string values
                        NamedPipeName = GetValue("NamedPipeName", "EmailProcessorPipe");
                        LogFilePath = GetValue("LogFilePath", LogFilePath);
                        LogLevel = GetValue("LogLevel", "Information");

                        // Load numeric values
                        if (int.TryParse(GetValue("ConnectionTimeoutSeconds", "10"), out var timeout))
                            ConnectionTimeoutSeconds = timeout;

                        if (long.TryParse(GetValue("MaxFileSizeBytes", "104857600"), out var maxFileSize))
                            MaxFileSizeBytes = maxFileSize;

                        if (int.TryParse(GetValue("RetryCount", "3"), out var retryCount))
                            RetryCount = retryCount;

                        if (int.TryParse(GetValue("RetryDelaySeconds", "2"), out var retryDelay))
                            RetryDelaySeconds = retryDelay;

                        // Load boolean values
                        ProcessReceivedEmails = bool.TryParse(GetValue("ProcessReceivedEmails", "true"), out var processReceived) && processReceived;
                        ProcessSentEmails = bool.TryParse(GetValue("ProcessSentEmails", "true"), out var processSent) && processSent;

                        // Load excluded file extensions
                        var excludedExtensions = GetValue("ExcludedFileExtensions", ".exe,.bat,.cmd,.scr,.pif,.com");
                        ExcludedFileExtensions = excludedExtensions.Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(ext => ext.Trim().ToLowerInvariant())
                            .ToList();
                    }
                    else
                    {
                        // Create default configuration
                        SaveConfiguration();
                    }
                }
            }
            catch (Exception)
            {
                // Use default values if registry access fails
            }
        }

        public void SaveConfiguration()
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(RegistryKeyPath))
                {
                    if (key != null)
                    {
                        key.SetValue("NamedPipeName", NamedPipeName);
                        key.SetValue("ConnectionTimeoutSeconds", ConnectionTimeoutSeconds.ToString());
                        key.SetValue("MaxFileSizeBytes", MaxFileSizeBytes.ToString());
                        key.SetValue("RetryCount", RetryCount.ToString());
                        key.SetValue("RetryDelaySeconds", RetryDelaySeconds.ToString());
                        key.SetValue("ProcessReceivedEmails", ProcessReceivedEmails.ToString());
                        key.SetValue("ProcessSentEmails", ProcessSentEmails.ToString());
                        key.SetValue("ExcludedFileExtensions", string.Join(",", ExcludedFileExtensions));
                        key.SetValue("LogFilePath", LogFilePath);
                        key.SetValue("LogLevel", LogLevel);
                    }
                }
            }
            catch (Exception)
            {
                // Ignore registry write errors
            }
        }

        public string GetValue(string key, string defaultValue = "")
        {
            try
            {
                using (var registryKey = Registry.CurrentUser.OpenSubKey(RegistryKeyPath))
                {
                    if (registryKey != null)
                    {
                        var value = registryKey.GetValue(key) as string;
                        return value ?? defaultValue;
                    }
                }
            }
            catch (Exception)
            {
                // Return default value if registry access fails
            }

            return defaultValue;
        }

        public void SetValue(string key, string value)
        {
            try
            {
                using (var registryKey = Registry.CurrentUser.CreateSubKey(RegistryKeyPath))
                {
                    if (registryKey != null)
                    {
                        registryKey.SetValue(key, value);
                    }
                }
            }
            catch (Exception)
            {
                // Ignore registry write errors
            }
        }
    }
} 