using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Infrastructure.Configuration
{
    /// <summary>
    /// Comprehensive configuration settings for the Email Attachment Processor
    /// </summary>
    public class EmailProcessorConfiguration
    {
        public StorageConfiguration Storage { get; set; } = new StorageConfiguration();
        public ProcessingConfiguration Processing { get; set; } = new ProcessingConfiguration();
        public DatabaseConfiguration Database { get; set; } = new DatabaseConfiguration();
        public LoggingConfiguration Logging { get; set; } = new LoggingConfiguration();
        public CommunicationConfiguration Communication { get; set; } = new CommunicationConfiguration();
    }

    /// <summary>
    /// Storage-related configuration settings
    /// </summary>
    public class StorageConfiguration
    {
        /// <summary>
        /// Base directory for local attachment storage
        /// </summary>
        public string LocalBasePath { get; set; } = @"C:\EmailAttachments";

        /// <summary>
        /// Base UNC path for network attachment storage
        /// </summary>
        public string UncBasePath { get; set; } = @"\\server\share\EmailAttachments";

        /// <summary>
        /// Maximum file size in bytes (default: 100MB)
        /// </summary>
        public long MaxFileSize { get; set; } = 104857600;

        /// <summary>
        /// Automatically create directories if they don't exist
        /// </summary>
        public bool CreateDirectories { get; set; } = true;

        /// <summary>
        /// Directory structure pattern for organizing attachments
        /// </summary>
        public string DirectoryStructure { get; set; } = @"{Year}\{Month}\{Day}\{EmailType}";

        /// <summary>
        /// UNC storage credentials username (optional)
        /// </summary>
        public string UncUsername { get; set; }

        /// <summary>
        /// UNC storage credentials password (optional)
        /// </summary>
        public string UncPassword { get; set; }

        /// <summary>
        /// Directory structure type (Basic or Enhanced)
        /// </summary>
        public DirectoryStructureType DirectoryStructureType { get; set; } = DirectoryStructureType.Basic;

        /// <summary>
        /// Name sanitization settings for enhanced directory structure
        /// </summary>
        public NameSanitizationSettings NameSanitization { get; set; } = new NameSanitizationSettings();

        /// <summary>
        /// Multiple recipients handling strategy for sent emails
        /// </summary>
        public MultipleRecipientsStrategy MultipleRecipientsStrategy { get; set; } = MultipleRecipientsStrategy.PrimaryOnly;

        /// <summary>
        /// Whether UNC storage is required for the application to start
        /// If false, the application will start with local storage only if UNC is not available
        /// </summary>
        public bool UncStorageRequired { get; set; } = false;
    }

    /// <summary>
    /// Processing-related configuration settings
    /// </summary>
    public class ProcessingConfiguration
    {
        /// <summary>
        /// Number of retry attempts for failed processing
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts in seconds
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 5;

        /// <summary>
        /// Maximum number of concurrent processing operations
        /// </summary>
        public int MaxConcurrentProcessing { get; set; } = 10;

        /// <summary>
        /// Timeout for processing operations in seconds
        /// </summary>
        public int ProcessingTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Email types to process (Received, Sent, or both)
        /// </summary>
        public EmailType[] ProcessEmailTypes { get; set; } = { EmailType.Received, EmailType.Sent };

        /// <summary>
        /// File extensions to exclude from processing
        /// </summary>
        public string[] ExcludedFileExtensions { get; set; } = { ".exe", ".bat", ".cmd", ".scr" };
    }

    /// <summary>
    /// Database-related configuration settings
    /// </summary>
    public class DatabaseConfiguration
    {
        /// <summary>
        /// SQL Server connection string
        /// </summary>
        public string ConnectionString { get; set; } = "Server=localhost;Database=EmailProcessor;Trusted_Connection=true;";

        /// <summary>
        /// Database command timeout in seconds
        /// </summary>
        public int CommandTimeout { get; set; } = 30;

        /// <summary>
        /// Enable automatic retry on database failures
        /// </summary>
        public bool EnableRetryOnFailure { get; set; } = true;

        /// <summary>
        /// Maximum retry attempts for database operations
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between database retry attempts in seconds
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 2;

        /// <summary>
        /// Maximum connection pool size
        /// </summary>
        public int MaxPoolSize { get; set; } = 100;

        /// <summary>
        /// Minimum connection pool size
        /// </summary>
        public int MinPoolSize { get; set; } = 0;

        /// <summary>
        /// Enable connection pooling
        /// </summary>
        public bool EnableConnectionPooling { get; set; } = true;

        /// <summary>
        /// Connection lifetime in seconds (0 = infinite)
        /// </summary>
        public int ConnectionLifetime { get; set; } = 300;

        /// <summary>
        /// Connection timeout in seconds
        /// </summary>
        public int ConnectionTimeout { get; set; } = 15;

        /// <summary>
        /// Enable connection resiliency
        /// </summary>
        public bool EnableConnectionResiliency { get; set; } = true;
    }

    /// <summary>
    /// Logging-related configuration settings
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// Minimum log level to record
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// Path for log file storage
        /// </summary>
        public string LogFilePath { get; set; } = @"C:\Logs\EmailProcessor\EmailProcessor-.log";

        /// <summary>
        /// Number of days to retain log files
        /// </summary>
        public int LogFileRetentionDays { get; set; } = 30;

        /// <summary>
        /// Enable console logging output
        /// </summary>
        public bool EnableConsoleLogging { get; set; } = false;

        /// <summary>
        /// Enable file logging output
        /// </summary>
        public bool EnableFileLogging { get; set; } = true;

        /// <summary>
        /// Use structured JSON logging format
        /// </summary>
        public bool UseStructuredLogging { get; set; } = false;

        /// <summary>
        /// Maximum size of each log file in bytes (default: 100MB)
        /// </summary>
        public long MaxLogFileSizeBytes { get; set; } = 100 * 1024 * 1024;

        /// <summary>
        /// Interval in seconds to flush logs to disk
        /// </summary>
        public int FlushIntervalSeconds { get; set; } = 5;
    }

    /// <summary>
    /// Communication-related configuration settings (VSTO to Windows Service)
    /// </summary>
    public class CommunicationConfiguration
    {
        /// <summary>
        /// Named pipe name for VSTO communication
        /// </summary>
        public string NamedPipeName { get; set; } = "EmailProcessorPipe";

        /// <summary>
        /// Connection timeout for named pipes in seconds
        /// </summary>
        public int ConnectionTimeoutSeconds { get; set; } = 10;

        /// <summary>
        /// Maximum message size for named pipe communication in bytes (default: 100MB)
        /// </summary>
        public long MaxMessageSize { get; set; } = 104857600;

        /// <summary>
        /// Number of retry attempts for communication failures
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Delay between communication retry attempts in seconds
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 2;

        /// <summary>
        /// Enable compression for large messages
        /// </summary>
        public bool EnableCompression { get; set; } = true;
    }
}