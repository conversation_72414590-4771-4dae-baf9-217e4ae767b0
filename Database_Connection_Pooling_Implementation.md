# Database Connection Pooling Implementation

## Overview

This document outlines the implementation of proper database connection pooling
and lifecycle management for the Email Processor Service. The changes address
thread safety concerns and improve database connection reliability.

## Key Changes Made

### 1. DbContext Lifetime Management

**Before:**

- DbContext registered as `Singleton` in dependency injection
- Potential thread safety issues with shared DbContext instances
- Improper connection lifecycle management

**After:**

- <PERSON>bContext registered as `Scoped` in dependency injection
- Each request/operation gets its own DbContext instance
- Proper disposal of DbContext instances when scope ends

### 2. Repository Lifetime Management

**Before:**

- All repositories registered as `Singleton`
- Repositories held references to singleton DbContext

**After:**

- All repositories registered as `Scoped` to match DbContext lifetime
- Ensures proper disposal and thread safety

### 3. Enhanced Database Configuration

New configuration properties added to `DatabaseConfiguration`:

```csharp
public class DatabaseConfiguration
{
    // Existing properties...

    /// <summary>
    /// Maximum connection pool size
    /// </summary>
    public int MaxPoolSize { get; set; } = 100;

    /// <summary>
    /// Minimum connection pool size
    /// </summary>
    public int MinPoolSize { get; set; } = 10;

    /// <summary>
    /// Enable connection pooling
    /// </summary>
    public bool EnableConnectionPooling { get; set; } = true;

    /// <summary>
    /// Connection lifetime in seconds (0 = infinite)
    /// </summary>
    public int ConnectionLifetime { get; set; } = 300;

    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// Enable connection resiliency
    /// </summary>
    public bool EnableConnectionResiliency { get; set; } = true;
}
```

### 4. Connection String Enhancement

The `BuildConnectionStringWithPooling` method dynamically configures connection
pooling parameters:

```csharp
private static string BuildConnectionStringWithPooling(DatabaseConfiguration dbConfig)
{
    var builder = new SqlConnectionStringBuilder(connectionString);

    // Configure connection pooling
    if (dbConfig?.EnableConnectionPooling == true)
    {
        builder.MaxPoolSize = dbConfig.MaxPoolSize;
        builder.MinPoolSize = dbConfig.MinPoolSize;
        builder.Pooling = true;

        // Set connection lifetime using the connection string format
        if (dbConfig.ConnectionLifetime > 0)
        {
            builder["Connection Lifetime"] = dbConfig.ConnectionLifetime;
        }
    }
    else
    {
        builder.Pooling = false;
    }

    // Configure connection timeout
    builder.ConnectTimeout = dbConfig?.ConnectionTimeout ?? 30;

    // Configure retry policy for connection resiliency
    if (dbConfig?.EnableConnectionResiliency == true)
    {
        builder.ConnectRetryCount = dbConfig.MaxRetryAttempts;
        builder.ConnectRetryInterval = dbConfig.RetryDelaySeconds;
    }

    return builder.ConnectionString;
}
```

### 5. Enhanced DbContext Configuration

Updated DbContext registration with improved configuration:

```csharp
services.AddScoped<EmailProcessorContext>(provider =>
{
    var dbConfig = configuration.GetSection("EmailProcessor:Database").Get<DatabaseConfiguration>();
    var optionsBuilder = new DbContextOptionsBuilder<EmailProcessorContext>();

    // Build connection string with pooling parameters
    var connectionString = BuildConnectionStringWithPooling(dbConfig);

    optionsBuilder.UseSqlServer(
        connectionString,
        sqlOptions =>
        {
            sqlOptions.CommandTimeout(dbConfig?.CommandTimeout ?? 30);

            // Configure connection pooling
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: dbConfig?.MaxRetryAttempts ?? 3,
                maxRetryDelay: TimeSpan.FromSeconds(dbConfig?.RetryDelaySeconds ?? 2),
                errorNumbersToAdd: null);

            // Configure connection resiliency and execution strategy
            sqlOptions.ExecutionStrategy(d => new SqlServerRetryingExecutionStrategy(d));
        });

    return new EmailProcessorContext(optionsBuilder.Options);
});
```

## Configuration Files Updated

### appsettings.json (Production)

```json
{
	"EmailProcessor": {
		"Database": {
			"ConnectionString": "Server=localhost,1433;Database=EmailProcessor;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;",
			"CommandTimeout": 30,
			"EnableRetryOnFailure": true,
			"MaxRetryAttempts": 3,
			"RetryDelaySeconds": 2,
			"MaxPoolSize": 100,
			"MinPoolSize": 10,
			"EnableConnectionPooling": true,
			"ConnectionLifetime": 300,
			"ConnectionTimeout": 30,
			"EnableConnectionResiliency": true
		}
	}
}
```

### appsettings.Development.json (Development)

```json
{
	"EmailProcessor": {
		"Database": {
			"ConnectionString": "Server=localhost,1433;Database=EmailProcessor;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;",
			"CommandTimeout": 30,
			"EnableRetryOnFailure": true,
			"MaxRetryAttempts": 2,
			"RetryDelaySeconds": 1,
			"MaxPoolSize": 50,
			"MinPoolSize": 5,
			"EnableConnectionPooling": true,
			"ConnectionLifetime": 180,
			"ConnectionTimeout": 15,
			"EnableConnectionResiliency": true
		}
	}
}
```

## Benefits of These Changes

### 1. Thread Safety

- DbContext instances are no longer shared across threads
- Each operation gets its own DbContext instance
- Eliminates potential race conditions and data corruption

### 2. Connection Pool Management

- Proper connection pooling configuration
- Configurable pool sizes for different environments
- Connection lifetime management to prevent stale connections

### 3. Connection Resiliency

- Automatic retry policies for transient failures
- Configurable retry attempts and delays
- Better handling of network interruptions

### 4. Resource Management

- Proper disposal of DbContext instances
- Automatic connection cleanup when scopes end
- Reduced memory leaks and resource exhaustion

### 5. Performance Optimization

- Connection reuse through pooling
- Reduced connection establishment overhead
- Better scalability under load

## Migration Considerations

### For Existing Deployments

1. **Database Connection String**: Update connection strings to include pooling
   parameters
2. **Configuration**: Add new database configuration properties to appsettings
   files
3. **Testing**: Verify that all database operations work correctly with scoped
   DbContext
4. **Monitoring**: Monitor connection pool usage and performance metrics

### Performance Impact

- **Positive**: Better connection reuse and reduced overhead
- **Neutral**: Slight increase in DbContext creation overhead (mitigated by
  pooling)
- **Monitoring**: Watch for any performance regressions in high-load scenarios

## Best Practices Implemented

1. **Scoped Lifetime**: DbContext and repositories use scoped lifetime for
   proper disposal
2. **Connection Pooling**: Configurable pool sizes appropriate for different
   environments
3. **Retry Policies**: Automatic retry for transient database failures
4. **Timeout Configuration**: Proper timeout settings for connections and
   commands
5. **Error Handling**: Enhanced error handling for connection-related issues
6. **Configuration Management**: All settings configurable through appsettings

## Monitoring and Troubleshooting

### Key Metrics to Monitor

- Connection pool utilization
- Connection timeout frequency
- Retry attempt counts
- DbContext disposal patterns

### Common Issues and Solutions

1. **Connection Pool Exhaustion**: Increase MaxPoolSize or reduce
   ConnectionLifetime
2. **Timeout Issues**: Adjust ConnectionTimeout and CommandTimeout values
3. **Retry Failures**: Review retry configuration and network stability
4. **Memory Leaks**: Ensure proper DbContext disposal in all code paths

## Conclusion

These changes significantly improve the reliability, performance, and thread
safety of the Email Processor Service's database operations. The implementation
follows Entity Framework Core best practices and provides a robust foundation
for handling database connections in a production environment.
