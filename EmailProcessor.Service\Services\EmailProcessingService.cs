using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Service.Models;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Service.Services
{
    /// <summary>
    /// Service responsible for orchestrating email processing workflow
    /// </summary>
    public class EmailProcessingService
    {
        private readonly IEmailRepository _emailRepository;
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IProcessingLogRepository _processingLogRepository;
        private readonly ILoggingProvider _loggingProvider;
        private readonly AttachmentHandlerService _attachmentHandlerService;
        private readonly DualStorageService _dualStorageService;

        public EmailProcessingService(
            IEmailRepository emailRepository,
            IAttachmentRepository attachmentRepository,
            IProcessingLogRepository processingLogRepository,
            ILoggingProvider loggingProvider,
            AttachmentHandlerService attachmentHandlerService,
            DualStorageService dualStorageService)
        {
            _emailRepository = emailRepository ?? throw new ArgumentNullException(nameof(emailRepository));
            _attachmentRepository = attachmentRepository ?? throw new ArgumentNullException(nameof(attachmentRepository));
            _processingLogRepository = processingLogRepository ?? throw new ArgumentNullException(nameof(processingLogRepository));
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            _attachmentHandlerService = attachmentHandlerService ?? throw new ArgumentNullException(nameof(attachmentHandlerService));
            _dualStorageService = dualStorageService ?? throw new ArgumentNullException(nameof(dualStorageService));
        }

        public async Task<EmailProcessingResponse> ProcessEmailAsync(EmailProcessingRequest request)
        {
            var correlationId = request.CorrelationId;
            var response = new EmailProcessingResponse
            {
                CorrelationId = correlationId,
                Timestamp = DateTime.UtcNow
            };

            try
            {
                await _loggingProvider.LogInformationAsync($"Starting email processing (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                // Validate request
                if (!ValidateRequest(request))
                {
                    response.Success = false;
                    response.Message = "Invalid request data";
                    await _loggingProvider.LogErrorAsync($"Invalid request data (CorrelationId: {correlationId})", "EmailProcessingService", null, correlationId);
                    return response;
                }

                // Check for duplicate email
                var existingEmail = await _emailRepository.GetByOutlookMessageIdAsync(request.Data.Email.OutlookMessageId);
                if (existingEmail != null)
                {
                    response.Success = false;
                    response.Message = "Email already processed";
                    response.EmailId = existingEmail.EmailId;
                    await _loggingProvider.LogWarningAsync($"Email already processed: {request.Data.Email.OutlookMessageId} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
                    return response;
                }

                // Create email entity
                var email = await CreateEmailEntityAsync(request.Data.Email, correlationId);
                if (email == null)
                {
                    response.Success = false;
                    response.Message = "Failed to create email entity";
                    await _loggingProvider.LogErrorAsync($"Failed to create email entity (CorrelationId: {correlationId})", "EmailProcessingService", null, correlationId);
                    return response;
                }

                // Save email to database
                email = await _emailRepository.AddAsync(email);

                response.EmailId = email.EmailId;
                await _loggingProvider.LogInformationAsync($"Email saved to database with ID: {email.EmailId} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                // Process attachments
                var attachmentResults = await ProcessAttachmentsAsync(email, request.Data.Attachments, correlationId);
                response.ProcessingResults = attachmentResults.Select(r => new Models.AttachmentProcessingResult
                {
                    FileName = r.FileName,
                    AttachmentId = r.AttachmentId,
                    Success = r.Success,
                    LocalStoragePath = r.LocalStoragePath,
                    UncStoragePath = r.UncStoragePath,
                    ErrorMessage = r.ErrorMessage
                }).ToList();

                // Update email processing status
                var allAttachmentsSuccessful = attachmentResults.All(r => r.Success);
                email.UpdateProcessingStatus(allAttachmentsSuccessful ? ProcessingStatus.Completed : ProcessingStatus.Failed);
                await _emailRepository.UpdateAsync(email);

                response.Success = allAttachmentsSuccessful;
                response.Message = allAttachmentsSuccessful ? "Email processing completed successfully" : "Email processing completed with errors";

                await _loggingProvider.LogInformationAsync($"Email processing completed. Success: {response.Success} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = "Email processing failed";
                response.ErrorDetails = ex.Message;

                await _loggingProvider.LogErrorAsync($"Email processing failed (CorrelationId: {correlationId})",  "EmailProcessingService", ex, correlationId);
            }

            return response;
        }

        private bool ValidateRequest(EmailProcessingRequest request)
        {
            if (request?.Data?.Email == null)
                return false;

            var email = request.Data.Email;
            return !string.IsNullOrWhiteSpace(email.Subject) &&
                   !string.IsNullOrWhiteSpace(email.OutlookMessageId) &&
                   !string.IsNullOrWhiteSpace(email.SenderEmail) &&
                   email.Timestamp != default;
        }

        private async Task<Email?> CreateEmailEntityAsync(EmailData emailData, Guid correlationId)
        {
            try
            {
                // Parse email type
                var emailType = emailData.EmailType.ToLower() switch
                {
                    "sent" => EmailType.Sent,
                    "received" => EmailType.Received,
                    _ => EmailType.Received
                };

                // Create email addresses
                var senderEmail = EmailAddress.Create(emailData.SenderEmail);
                var recipientTo = emailData.RecipientTo.Select(EmailAddress.Create).ToList();
                var recipientCC = emailData.RecipientCC.Select(EmailAddress.Create).ToList();

                // Create email entity
                var email = new Email(
                    emailData.Subject,
                    emailData.SenderName,
                    senderEmail,
                    recipientTo,
                    recipientCC,
                    emailType,
                    emailData.Timestamp,
                    emailData.OutlookMessageId);

                await _loggingProvider.LogDebugAsync($"Email entity created for: {emailData.Subject} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                return email;
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error creating email entity (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                return null;
            }
        }

        private async Task<List<AttachmentProcessingResult>> ProcessAttachmentsAsync(
            Email email, 
            List<AttachmentData> attachments, 
            Guid correlationId)
        {
            var results = new List<AttachmentProcessingResult>();

            if (attachments == null || !attachments.Any())
            {
                await _loggingProvider.LogInformationAsync($"No attachments to process for email: {email.EmailId} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
                return results;
            }

            await _loggingProvider.LogInformationAsync($"Processing {attachments.Count} attachments for email: {email.EmailId} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

            // Process attachments in parallel for better performance
            var processingTasks = attachments.Select(async attachmentData =>
            {
                var result = new AttachmentProcessingResult
                {
                    FileName = attachmentData.FileName
                };

                try
                {
                    // Create attachment entity
                    var attachment = new Attachment(
                        email.EmailId,
                        attachmentData.FileName,
                        attachmentData.OriginalFileName,
                        attachmentData.ContentType,
                        attachmentData.FileExtension,
                        attachmentData.FileSize);

                    // Save attachment to database
                    attachment = await _attachmentRepository.AddAsync(attachment);

                    result.AttachmentId = attachment.AttachmentId;

                    // Process attachment storage
                    var storageResult = await ProcessAttachmentStorageAsync(attachment, attachmentData, correlationId);
                    
                    if (storageResult.IsSuccessful)
                    {
                        // Update attachment with storage paths
                        if (storageResult.LocalStorageResult.IsSuccessful)
                        {
                            attachment.SetLocalStoragePath(FilePath.Create(storageResult.LocalStorageResult.FilePath));
                            attachment.UpdateLocalProcessingStatus(ProcessingStatus.Completed);
                        }

                        if (storageResult.UncStorageResult.IsSuccessful)
                        {
                            attachment.SetUncStoragePath(FilePath.Create(storageResult.UncStorageResult.FilePath));
                            attachment.UpdateUncProcessingStatus(ProcessingStatus.Completed);
                        }

                        await _attachmentRepository.UpdateAsync(attachment);

                        result.Success = true;
                        result.LocalStoragePath = storageResult.LocalStorageResult.FilePath;
                        result.UncStoragePath = storageResult.UncStorageResult.FilePath;

                        await _loggingProvider.LogInformationAsync($"Attachment processed successfully: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
                    }
                    else
                    {
                        // Update attachment with error status
                        if (!storageResult.LocalStorageResult.IsSuccessful)
                        {
                            attachment.UpdateLocalProcessingStatus(ProcessingStatus.Failed, storageResult.LocalStorageResult.ErrorMessage);
                        }

                        if (!storageResult.UncStorageResult.IsSuccessful)
                        {
                            attachment.UpdateUncProcessingStatus(ProcessingStatus.Failed, storageResult.UncStorageResult.ErrorMessage);
                        }

                        await _attachmentRepository.UpdateAsync(attachment);

                        result.Success = false;
                        result.ErrorMessage = $"Storage failed. Local: {storageResult.LocalStorageResult.IsSuccessful}, UNC: {storageResult.UncStorageResult.IsSuccessful}";

                        await _loggingProvider.LogErrorAsync($"Attachment storage failed: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", null, correlationId);
                    }
                }
                catch (Exception ex)
                {
                    result.Success = false;
                    result.ErrorMessage = ex.Message;

                    await _loggingProvider.LogErrorAsync($"Error processing attachment: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                }

                return result;
            });

            // Wait for all attachment processing to complete
            var attachmentResults = await Task.WhenAll(processingTasks);
            results.AddRange(attachmentResults);

            await _loggingProvider.LogInformationAsync($"Completed processing {results.Count} attachments. Successful: {results.Count(r => r.Success)} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

            return results;
        }

        private async Task<DualStorageResult> ProcessAttachmentStorageAsync(
            Attachment attachment, 
            AttachmentData attachmentData, 
            Guid correlationId)
        {
            try
            {
                // Decode base64 file data
                var fileData = Convert.FromBase64String(attachmentData.FileData);

                // Create directory structure based on email type and date
                var subDirectory = CreateDirectoryStructure(attachment.Email.EmailType, attachment.Email.Timestamp);

                // Save to dual storage
                var storageResult = await _dualStorageService.SaveFileAsync(
                    attachmentData.FileName,
                    fileData,
                    subDirectory);

                await _loggingProvider.LogDebugAsync($"Storage result for {attachmentData.FileName}: Local={storageResult.LocalStorageResult.IsSuccessful}, UNC={storageResult.UncStorageResult.IsSuccessful} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                return storageResult;
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error in attachment storage processing: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                
                return new DualStorageResult
                {
                    FileName = attachmentData.FileName,
                    IsSuccessful = false,
                    ErrorMessage = ex.Message,
                    LocalStorageResult = new StorageResult { StorageType = StorageType.Local, IsSuccessful = false, ErrorMessage = ex.Message },
                    UncStorageResult = new StorageResult { StorageType = StorageType.Unc, IsSuccessful = false, ErrorMessage = ex.Message }
                };
            }
        }

        private string CreateDirectoryStructure(EmailType emailType, DateTime timestamp)
        {
            var year = timestamp.Year.ToString();
            var month = timestamp.Month.ToString("00");
            var day = timestamp.Day.ToString("00");
            var emailTypeFolder = emailType == EmailType.Received ? "Received" : "Sent";

            return Path.Combine(year, month, day, emailTypeFolder);
        }
    }
} 