using System;
using System.IO;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Infrastructure.Storage.UncStorage
{
    /// <summary>
    /// Null storage provider that does nothing - used as a fallback when UNC storage is not available
    /// </summary>
    public class NullStorageProvider : IStorageProvider
    {
        private readonly ILoggingProvider _loggingProvider;

        public NullStorageProvider(ILoggingProvider loggingProvider)
        {
            _loggingProvider = loggingProvider;
        }

        public StorageType StorageType => StorageType.Unc;

        public string BasePath => string.Empty;

        public async Task<FilePath> SaveFileAsync(byte[] fileData, string fileName, string directoryPath)
        {
            _loggingProvider.LogWarning($"Attempted to save file {fileName} to UNC storage, but UNC storage is not available.");
            throw new InvalidOperationException("UNC storage is not available");
        }

        public async Task<FilePath> SaveFileAsync(Stream fileStream, string fileName, string directoryPath)
        {
            _loggingProvider.LogWarning($"Attempted to save file {fileName} to UNC storage, but UNC storage is not available.");
            throw new InvalidOperationException("UNC storage is not available");
        }

        public async Task CreateDirectoryAsync(string directoryPath)
        {
            _loggingProvider.LogWarning($"Attempted to create directory {directoryPath} in UNC storage, but UNC storage is not available.");
            throw new InvalidOperationException("UNC storage is not available");
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            _loggingProvider.LogDebug($"Checking file existence {filePath} in UNC storage, but UNC storage is not available.");
            return false;
        }

        public async Task<bool> DirectoryExistsAsync(string directoryPath)
        {
            _loggingProvider.LogDebug($"Checking directory existence {directoryPath} in UNC storage, but UNC storage is not available.");
            return false;
        }

        public async Task DeleteFileAsync(string filePath)
        {
            _loggingProvider.LogWarning($"Attempted to delete file {filePath} from UNC storage, but UNC storage is not available.");
            throw new InvalidOperationException("UNC storage is not available");
        }

        public async Task<FileInfo> GetFileInfoAsync(string filePath)
        {
            _loggingProvider.LogWarning($"Attempted to get file info for {filePath} from UNC storage, but UNC storage is not available.");
            throw new InvalidOperationException("UNC storage is not available");
        }

        public async Task<long> GetAvailableSpaceAsync()
        {
            _loggingProvider.LogDebug("Checking available space in UNC storage, but UNC storage is not available.");
            return 0;
        }

        public async Task<bool> TestConnectivityAsync()
        {
            _loggingProvider.LogDebug("Testing UNC storage connectivity, but UNC storage is not available.");
            return false;
        }
    }
} 