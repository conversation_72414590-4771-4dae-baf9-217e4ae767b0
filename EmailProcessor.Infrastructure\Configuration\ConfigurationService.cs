using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Infrastructure.Configuration
{
    /// <summary>
    /// Configuration service that manages application settings with validation and encryption support
    /// </summary>
    public class ConfigurationService : IConfigurationService, IDisposable
    {
        private readonly ILoggingProvider _loggingProvider;
        private readonly IConfigurationRepository _configurationRepository;
        private readonly Dictionary<string, object> _configurationCache;
        private readonly object _cacheLock = new object();
        private readonly EmailProcessorConfiguration _defaultConfiguration;

        public ConfigurationService(
            IConfigurationRepository configurationRepository, 
            ILoggingProvider loggingProvider = null,
            EmailProcessorConfiguration defaultConfiguration = null)
        {
            _configurationRepository = configurationRepository ?? throw new ArgumentNullException(nameof(configurationRepository));
            _loggingProvider = loggingProvider;
            _configurationCache = new Dictionary<string, object>();
            _defaultConfiguration = defaultConfiguration ?? new EmailProcessorConfiguration();

            _loggingProvider?.LogInformation("ConfigurationService initialized");
        }

        public async Task<T> GetConfigurationAsync<T>(string key, T defaultValue = default)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("Configuration key cannot be null or empty", nameof(key));

            try
            {
                // Check cache first
                lock (_cacheLock)
                {
                    if (_configurationCache.TryGetValue(key, out var cachedValue))
                    {
                        return (T)cachedValue;
                    }
                }

                // Get from repository
                var configEntity = await _configurationRepository.GetByKeyAsync(key);
                if (configEntity != null)
                {
                    var value = DeserializeValue<T>(configEntity.ConfigValue, configEntity.IsEncrypted);
                    
                    // Update cache
                    lock (_cacheLock)
                    {
                        _configurationCache[key] = value;
                    }

                    _loggingProvider?.LogDebug($"Configuration value retrieved: {key}");
                    return value;
                }

                // Return default value if not found
                _loggingProvider?.LogWarning($"Configuration key not found, using default: {key}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError($"Error retrieving configuration for key: {key}", ex);
                return defaultValue;
            }
        }

        public async Task SetConfigurationAsync<T>(string key, T value, string description = null, bool isEncrypted = false)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("Configuration key cannot be null or empty", nameof(key));

            try
            {
                var serializedValue = SerializeValue(value, isEncrypted);
                
                var configEntity = await _configurationRepository.GetByKeyAsync(key);
                if (configEntity != null)
                {
                    // Update existing
                    configEntity.ConfigValue = serializedValue;
                    configEntity.Description = description ?? configEntity.Description;
                    configEntity.IsEncrypted = isEncrypted;
                    configEntity.ModifiedDate = DateTime.UtcNow;
                    
                    await _configurationRepository.UpdateAsync(configEntity);
                }
                else
                {
                    // Create new
                    configEntity = new Domain.Entities.Configuration(key, serializedValue, description, isEncrypted);
                    await _configurationRepository.AddAsync(configEntity);
                }

                // Update cache
                lock (_cacheLock)
                {
                    _configurationCache[key] = value;
                }

                _loggingProvider?.LogInformation($"Configuration value set: {key}");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError($"Error setting configuration for key: {key}", ex);
                throw;
            }
        }

        public async Task<bool> DeleteConfigurationAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            try
            {
                var configEntity = await _configurationRepository.GetByKeyAsync(key);
                if (configEntity != null)
                {
                    await _configurationRepository.DeleteAsync(configEntity);
                    
                    // Remove from cache
                    lock (_cacheLock)
                    {
                        _configurationCache.Remove(key);
                    }

                    _loggingProvider?.LogInformation($"Configuration deleted: {key}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError($"Error deleting configuration for key: {key}", ex);
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetAllConfigurationsAsync()
        {
            try
            {
                var allConfigs = await _configurationRepository.GetAllAsync();
                var result = new Dictionary<string, object>();

                foreach (var config in allConfigs)
                {
                    try
                    {
                        var value = DeserializeValue<object>(config.ConfigValue, config.IsEncrypted);
                        result[config.ConfigKey] = value;
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider?.LogWarning($"Error deserializing configuration {config.ConfigKey}: {ex.Message}");
                        result[config.ConfigKey] = config.ConfigValue; // Return raw value as fallback
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error retrieving all configurations", ex);
                return new Dictionary<string, object>();
            }
        }

        public async Task<EmailProcessorConfiguration> GetEmailProcessorConfigurationAsync()
        {
            try
            {
                var config = new EmailProcessorConfiguration();

                // Storage settings
                config.Storage.LocalBasePath = await GetConfigurationAsync("Storage.LocalBasePath", _defaultConfiguration.Storage.LocalBasePath);
                config.Storage.UncBasePath = await GetConfigurationAsync("Storage.UncBasePath", _defaultConfiguration.Storage.UncBasePath);
                config.Storage.MaxFileSize = await GetConfigurationAsync("Storage.MaxFileSize", _defaultConfiguration.Storage.MaxFileSize);
                config.Storage.CreateDirectories = await GetConfigurationAsync("Storage.CreateDirectories", _defaultConfiguration.Storage.CreateDirectories);

                // Processing settings
                config.Processing.RetryCount = await GetConfigurationAsync("Processing.RetryCount", _defaultConfiguration.Processing.RetryCount);
                config.Processing.RetryDelaySeconds = await GetConfigurationAsync("Processing.RetryDelaySeconds", _defaultConfiguration.Processing.RetryDelaySeconds);
                config.Processing.MaxConcurrentProcessing = await GetConfigurationAsync("Processing.MaxConcurrentProcessing", _defaultConfiguration.Processing.MaxConcurrentProcessing);
                config.Processing.ProcessingTimeoutSeconds = await GetConfigurationAsync("Processing.ProcessingTimeoutSeconds", _defaultConfiguration.Processing.ProcessingTimeoutSeconds);

                // Database settings
                config.Database.ConnectionString = await GetConfigurationAsync("Database.ConnectionString", _defaultConfiguration.Database.ConnectionString);
                config.Database.CommandTimeout = await GetConfigurationAsync("Database.CommandTimeout", _defaultConfiguration.Database.CommandTimeout);
                config.Database.EnableRetryOnFailure = await GetConfigurationAsync("Database.EnableRetryOnFailure", _defaultConfiguration.Database.EnableRetryOnFailure);

                // Logging settings
                config.Logging.LogLevel = await GetConfigurationAsync("Logging.LogLevel", _defaultConfiguration.Logging.LogLevel);
                config.Logging.LogFilePath = await GetConfigurationAsync("Logging.LogFilePath", _defaultConfiguration.Logging.LogFilePath);
                config.Logging.LogFileRetentionDays = await GetConfigurationAsync("Logging.LogFileRetentionDays", _defaultConfiguration.Logging.LogFileRetentionDays);
                config.Logging.EnableConsoleLogging = await GetConfigurationAsync("Logging.EnableConsoleLogging", _defaultConfiguration.Logging.EnableConsoleLogging);

                // Communication settings
                config.Communication.NamedPipeName = await GetConfigurationAsync("Communication.NamedPipeName", _defaultConfiguration.Communication.NamedPipeName);
                config.Communication.ConnectionTimeoutSeconds = await GetConfigurationAsync("Communication.ConnectionTimeoutSeconds", _defaultConfiguration.Communication.ConnectionTimeoutSeconds);
                config.Communication.MaxMessageSize = await GetConfigurationAsync("Communication.MaxMessageSize", _defaultConfiguration.Communication.MaxMessageSize);

                return config;
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error loading EmailProcessor configuration, using defaults", ex);
                return _defaultConfiguration;
            }
        }

        public async Task InitializeDefaultConfigurationAsync()
        {
            try
            {
                _loggingProvider?.LogInformation("Initializing default configuration values");

                var defaultConfigs = new Dictionary<string, (object value, string description, bool isEncrypted)>
                {
                    // Storage configuration
                    { "Storage.LocalBasePath", (_defaultConfiguration.Storage.LocalBasePath, "Base directory for local attachment storage", false) },
                    { "Storage.UncBasePath", (_defaultConfiguration.Storage.UncBasePath, "Base UNC path for network attachment storage", false) },
                    { "Storage.MaxFileSize", (_defaultConfiguration.Storage.MaxFileSize, "Maximum file size in bytes (100MB)", false) },
                    { "Storage.CreateDirectories", (_defaultConfiguration.Storage.CreateDirectories, "Automatically create directories if they don't exist", false) },

                    // Processing configuration
                    { "Processing.RetryCount", (_defaultConfiguration.Processing.RetryCount, "Number of retry attempts for failed processing", false) },
                    { "Processing.RetryDelaySeconds", (_defaultConfiguration.Processing.RetryDelaySeconds, "Delay between retry attempts in seconds", false) },
                    { "Processing.MaxConcurrentProcessing", (_defaultConfiguration.Processing.MaxConcurrentProcessing, "Maximum number of concurrent processing operations", false) },
                    { "Processing.ProcessingTimeoutSeconds", (_defaultConfiguration.Processing.ProcessingTimeoutSeconds, "Timeout for processing operations in seconds", false) },

                    // Database configuration
                    { "Database.ConnectionString", (_defaultConfiguration.Database.ConnectionString, "SQL Server connection string", true) },
                    { "Database.CommandTimeout", (_defaultConfiguration.Database.CommandTimeout, "Database command timeout in seconds", false) },
                    { "Database.EnableRetryOnFailure", (_defaultConfiguration.Database.EnableRetryOnFailure, "Enable automatic retry on database failures", false) },

                    // Logging configuration
                    { "Logging.LogLevel", (_defaultConfiguration.Logging.LogLevel.ToString(), "Minimum log level to record", false) },
                    { "Logging.LogFilePath", (_defaultConfiguration.Logging.LogFilePath, "Path for log file storage", false) },
                    { "Logging.LogFileRetentionDays", (_defaultConfiguration.Logging.LogFileRetentionDays, "Number of days to retain log files", false) },
                    { "Logging.EnableConsoleLogging", (_defaultConfiguration.Logging.EnableConsoleLogging, "Enable console logging output", false) },

                    // Communication configuration
                    { "Communication.NamedPipeName", (_defaultConfiguration.Communication.NamedPipeName, "Named pipe name for VSTO communication", false) },
                    { "Communication.ConnectionTimeoutSeconds", (_defaultConfiguration.Communication.ConnectionTimeoutSeconds, "Connection timeout for named pipes", false) },
                    { "Communication.MaxMessageSize", (_defaultConfiguration.Communication.MaxMessageSize, "Maximum message size for named pipe communication", false) }
                };

                foreach (var (key, (value, description, isEncrypted)) in defaultConfigs)
                {
                    var existing = await _configurationRepository.GetByKeyAsync(key);
                    if (existing == null)
                    {
                        await SetConfigurationAsync(key, value, description, isEncrypted);
                    }
                }

                _loggingProvider?.LogInformation($"Default configuration initialization completed. {defaultConfigs.Count} settings processed.");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error initializing default configuration", ex);
                throw;
            }
        }

        public void ClearCache()
        {
            lock (_cacheLock)
            {
                _configurationCache.Clear();
            }
            _loggingProvider?.LogInformation("Configuration cache cleared");
        }

        private string SerializeValue<T>(T value, bool isEncrypted)
        {
            var json = JsonSerializer.Serialize(value);
            
            if (isEncrypted)
            {
                // TODO: Implement encryption when needed
                // For now, just return the JSON - encryption can be added later
                return json;
            }

            return json;
        }

        private T DeserializeValue<T>(string serializedValue, bool isEncrypted)
        {
            if (isEncrypted)
            {
                // TODO: Implement decryption when needed
                // For now, just deserialize as if not encrypted
            }

            if (typeof(T) == typeof(string))
            {
                // Handle string values that might not be JSON-serialized
                return (T)(object)serializedValue.Trim('"');
            }

            return JsonSerializer.Deserialize<T>(serializedValue);
        }

        public void Dispose()
        {
            // Nothing to dispose currently, but available for future use
        }
    }
}