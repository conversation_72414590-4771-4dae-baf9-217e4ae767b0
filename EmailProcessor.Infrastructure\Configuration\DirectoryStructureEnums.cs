namespace EmailProcessor.Infrastructure.Configuration
{
    /// <summary>
    /// Directory structure type for organizing email attachments
    /// </summary>
    public enum DirectoryStructureType
    {
        /// <summary>
        /// Basic structure: {Year}\{Month}\{Day}\{EmailType}
        /// </summary>
        Basic,

        /// <summary>
        /// Enhanced structure: {Year}\{Month}\{Day}\{EmailType}\{SenderName/RecipientName}
        /// </summary>
        Enhanced
    }

    /// <summary>
    /// Strategy for handling multiple recipients in sent emails
    /// </summary>
    public enum MultipleRecipientsStrategy
    {
        /// <summary>
        /// Use only the primary (first) recipient
        /// </summary>
        PrimaryOnly,

        /// <summary>
        /// Combine multiple recipients into a single folder name
        /// </summary>
        Combined,

        /// <summary>
        /// Create separate folders for each recipient
        /// </summary>
        SeparateFolders
    }
}
