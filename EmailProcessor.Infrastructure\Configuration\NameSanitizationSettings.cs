namespace EmailProcessor.Infrastructure.Configuration
{
    /// <summary>
    /// Configuration settings for sanitizing sender/recipient names for file system compatibility
    /// </summary>
    public class NameSanitizationSettings
    {
        /// <summary>
        /// Maximum length for sanitized names (default: 50)
        /// </summary>
        public int MaxLength { get; set; } = 50;

        /// <summary>
        /// Character to replace spaces with (default: "_")
        /// </summary>
        public string ReplaceSpacesWith { get; set; } = "_";

        /// <summary>
        /// Remove special characters that are invalid for file system (default: true)
        /// </summary>
        public bool RemoveSpecialCharacters { get; set; } = true;

        /// <summary>
        /// Use email address as fallback when name is empty (default: true)
        /// </summary>
        public bool UseEmailAsFallback { get; set; } = true;

        /// <summary>
        /// Preserve Unicode characters instead of converting to ASCII (default: false)
        /// </summary>
        public bool PreserveUnicode { get; set; } = false;

        /// <summary>
        /// Characters to remove from names (in addition to invalid file system characters)
        /// </summary>
        public string[] AdditionalInvalidCharacters { get; set; } = new string[] { };

        /// <summary>
        /// Fallback name when all sanitization fails (default: "Unknown_User")
        /// </summary>
        public string FallbackName { get; set; } = "Unknown_User";
    }
}
