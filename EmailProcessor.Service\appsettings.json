{"EmailProcessor": {"Storage": {"LocalBasePath": "C:\\EmailAttachments", "UncBasePath": "\\\\server\\share\\EmailAttachments", "MaxFileSize": 104857600, "CreateDirectories": true, "DirectoryStructure": "{Year}\\{Month}\\{Day}\\{EmailType}", "DirectoryStructureType": "Enhanced", "MultipleRecipientsStrategy": "PrimaryOnly", "NameSanitization": {"MaxLength": 50, "ReplaceSpacesWith": "_", "RemoveSpecialCharacters": true, "PreserveUnicode": false, "UseEmailAsFallback": true, "FallbackName": "Unknown", "AdditionalInvalidCharacters": ["#", "%", "&", "*", ":", "<", ">", "?", "/", "\\", "{", "|", "}"]}, "UncStorageRequired": false, "UncUsername": "username", "UncPassword": "password"}, "Processing": {"RetryCount": 3, "RetryDelaySeconds": 5, "MaxConcurrentProcessing": 10, "ProcessingTimeoutSeconds": 30, "ProcessEmailTypes": ["Received", "<PERSON><PERSON>"], "ExcludedFileExtensions": [".exe", ".bat", ".cmd", ".scr"]}, "Database": {"ConnectionString": "Server=localhost,1433;Database=EmailProcessor;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;", "CommandTimeout": 30, "EnableRetryOnFailure": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "MaxPoolSize": 100, "MinPoolSize": 0, "EnableConnectionPooling": true, "ConnectionLifetime": 300, "ConnectionTimeout": 15, "EnableConnectionResiliency": true}, "Logging": {"LogLevel": "Information", "LogFilePath": "C:\\Logs\\EmailProcessor\\EmailProcessor-.log", "LogFileRetentionDays": 30, "EnableConsoleLogging": false, "EnableFileLogging": true, "UseStructuredLogging": false, "MaxLogFileSizeBytes": 104857600, "FlushIntervalSeconds": 5}, "Communication": {"NamedPipeName": "EmailProcessorPipe", "ConnectionTimeoutSeconds": 10, "MaxMessageSize": 104857600, "RetryCount": 3, "RetryDelaySeconds": 2, "EnableCompression": true}}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "C:\\Logs\\EmailProcessor\\EmailProcessor-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "shared": true, "flushToDiskInterval": "00:00:05", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj} {CorrelationId} {NewLine}{Exception}"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj} {CorrelationId} {NewLine}{Exception}"}}], "Enrich": ["FromLogContext"], "Properties": {"Application": "EmailProcessor.Service"}}}