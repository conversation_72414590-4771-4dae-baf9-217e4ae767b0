using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using EmailProcessor.Infrastructure.Configuration;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Logging;
using EmailProcessor.Service.Communication;
using EmailProcessor.Service.Services;
using EmailProcessor.Domain.Interfaces;

namespace EmailProcessor.Service.Services
{
    /// <summary>
    /// Main hosted service that orchestrates the Email Processor Windows Service
    /// </summary>
    public class EmailProcessorHostedService : BackgroundService
    {
        private readonly ILogger<EmailProcessorHostedService> _logger;
        private readonly EmailProcessorConfiguration _configuration;
        private readonly NamedPipeServer _namedPipeServer;
        private readonly ILoggingProvider _loggingProvider;
        private readonly DualStorageService _dualStorageService;
        private readonly IServiceProvider _serviceProvider;

        public EmailProcessorHostedService(
            ILogger<EmailProcessorHostedService> logger,
            IOptions<EmailProcessorConfiguration> configuration,
            NamedPipeServer namedPipeServer,
            ILoggingProvider loggingProvider,
            DualStorageService dualStorageService,
            IServiceProvider serviceProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
            _namedPipeServer = namedPipeServer ?? throw new ArgumentNullException(nameof(namedPipeServer));
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            _dualStorageService = dualStorageService ?? throw new ArgumentNullException(nameof(dualStorageService));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var correlationId = Guid.NewGuid();
            
            try
            {
                _logger.LogInformation("Email Processor Service starting... (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogInformationAsync("Email Processor Service starting", "EmailProcessorHostedService", correlationId);

                // Initialize configuration
                await _configurationService.InitializeDefaultConfigurationAsync();

                // Test storage connectivity
                await TestStorageConnectivityAsync(correlationId);

                // Start Named Pipe server
                await StartNamedPipeServerAsync(stoppingToken);

                // Keep the service running
                await KeepAliveAsync(stoppingToken);

                _logger.LogInformation("Email Processor Service stopped gracefully (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogInformationAsync("Email Processor Service stopped gracefully", "EmailProcessorHostedService", correlationId);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Email Processor Service cancellation requested (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogInformationAsync("Email Processor Service cancellation requested", "EmailProcessorHostedService", correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Email Processor Service encountered an error (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogErrorAsync("Email Processor Service encountered an error", "EmailProcessorHostedService", ex, correlationId);
                throw;
            }
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Email Processor Service starting...");
            await base.StartAsync(cancellationToken);
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Email Processor Service stopping...");
            
            // Stop Named Pipe server
            if (_namedPipeServer != null)
            {
                await _namedPipeServer.StopAsync();
            }
            
            await base.StopAsync(cancellationToken);
        }

        private async Task InitializeConfigurationAsync(Guid correlationId)
        {
            try
            {
                _logger.LogInformation("Initializing configuration... (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogInformationAsync("Initializing configuration", "EmailProcessorHostedService", correlationId);

                // Load configuration from database
                await _configurationService.InitializeDefaultConfigurationAsync();

                _logger.LogInformation("Configuration initialized successfully (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogInformationAsync("Configuration initialized successfully", "EmailProcessorHostedService", correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize configuration (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogErrorAsync("Failed to initialize configuration", "EmailProcessorHostedService", ex, correlationId);
                throw;
            }
        }

        private async Task TestStorageConnectivityAsync(Guid correlationId)
        {
            try
            {
                _logger.LogInformation("Testing storage connectivity... (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogInformationAsync("Testing storage connectivity", "EmailProcessorHostedService", correlationId);

                var connectivityResult = await _dualStorageService.TestConnectivityAsync();
                if (!connectivityResult)
                {
                    throw new InvalidOperationException("Storage connectivity test failed");
                }

                var healthStatus = await _dualStorageService.GetStorageHealthStatusAsync();
                _logger.LogInformation("Storage health status - Local: {LocalAvailable}, UNC: {UncAvailable}, Local Space: {LocalSpace}, UNC Space: {UncSpace} (CorrelationId: {CorrelationId})", 
                    healthStatus.LocalStorageAvailable, healthStatus.UncStorageAvailable, 
                    healthStatus.LocalAvailableSpace, healthStatus.UncAvailableSpace, correlationId);

                // Log storage mode information
                var storageMode = healthStatus.UncStorageAvailable ? "Dual Storage (Local + UNC)" : "Local Storage Only";
                _logger.LogInformation("Storage mode: {StorageMode} (CorrelationId: {CorrelationId})", storageMode, correlationId);

                await _loggingProvider.LogInformationAsync($"Storage connectivity test successful. Local: {healthStatus.LocalStorageAvailable}, UNC: {healthStatus.UncStorageAvailable}", 
                    "EmailProcessorHostedService", correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Storage connectivity test failed (CorrelationId: {CorrelationId})", correlationId);
                await _loggingProvider.LogErrorAsync("Storage connectivity test failed",  "EmailProcessorHostedService", ex, correlationId);
                throw;
            }
        }

        private async Task StartNamedPipeServerAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("Starting Named Pipe server...");
                await _loggingProvider.LogInformationAsync("Starting Named Pipe server", "EmailProcessorHostedService");

                // Configure message handlers with scoped service resolution
                _namedPipeServer.OnEmailProcessingRequest += async (request) =>
                {
                    try
                    {
                        // Create a scope for each request to properly handle scoped services
                        using var scope = _serviceProvider.CreateScope();
                        var emailProcessingService = scope.ServiceProvider.GetRequiredService<EmailProcessingService>();
                        await emailProcessingService.ProcessEmailAsync(request);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing email request");
                        await _loggingProvider.LogErrorAsync("Error processing email request", "EmailProcessingService", ex);
                    }
                };

                // Start the server
                await _namedPipeServer.StartAsync();

                _logger.LogInformation("Named Pipe server started successfully");
                await _loggingProvider.LogInformationAsync("Named Pipe server started successfully", "EmailProcessorHostedService");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start Named Pipe server");
                await _loggingProvider.LogErrorAsync("Failed to start Named Pipe server",  "EmailProcessorHostedService", ex);
                throw;
            }
        }

        private async Task KeepAliveAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Email Processor Service is running. Press Ctrl+C to stop.");
            await _loggingProvider.LogInformationAsync("Email Processor Service is running", "EmailProcessorHostedService");

            // Keep the service running until cancellation is requested
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Perform periodic health checks
                    await PerformHealthCheckAsync();
                    
                    // Wait for 30 seconds before next health check
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during health check");
                    await _loggingProvider.LogErrorAsync("Error during health check", "EmailProcessorHostedService", ex);
                    
                    // Wait a bit before retrying
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
            }
        }

        private async Task PerformHealthCheckAsync()
        {
            try
            {
                // Check storage health
                var storageHealth = await _dualStorageService.GetStorageHealthStatusAsync();
                
                // Check Named Pipe server status
                var serverStatus = _namedPipeServer.IsRunning;

                // Check if UNC storage is required
                var uncStorageRequired = _configuration.Storage.UncStorageRequired;

                // Determine if health check passes
                var localStorageOk = storageHealth.LocalStorageAvailable;
                var uncStorageOk = !uncStorageRequired || storageHealth.UncStorageAvailable;
                var serverOk = serverStatus;

                if (!localStorageOk || !uncStorageOk || !serverOk)
                {
                    _logger.LogWarning("Health check failed - Local Storage: {Local}, UNC Storage: {Unc} (Required: {UncRequired}), Server: {Server}", 
                        storageHealth.LocalStorageAvailable, storageHealth.UncStorageAvailable, uncStorageRequired, serverStatus);
                    await _loggingProvider.LogWarningAsync($"Health check failed - Local: {storageHealth.LocalStorageAvailable}, UNC: {storageHealth.UncStorageAvailable} (Required: {uncStorageRequired}), Server: {serverStatus}", 
                        "EmailProcessorHostedService");
                }
                else
                {
                    _logger.LogDebug("Health check passed - All systems operational");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                await _loggingProvider.LogErrorAsync("Health check failed", "EmailProcessorHostedService", ex);
            }
        }
    }
} 