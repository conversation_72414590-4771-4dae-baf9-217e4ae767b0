using System;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace EmailProcessor.Infrastructure.Database
{
    /// <summary>
    /// Helper class for database connection management and error handling
    /// </summary>
    public static class DatabaseConnectionHelper
    {
        /// <summary>
        /// SQL Server error numbers that are considered transient and should be retried
        /// </summary>
        private static readonly int[] TransientErrorNumbers = new[]
        {
            2,      // Timeout expired  
            20,     // Instance failure
            64,     // Connection lost
            233,    // Connection initialization error
            10053,  // Transport endpoint is not connected
            10054,  // Connection reset by peer
            10060,  // Connection timeout
            40197,  // Service has encountered an error processing your request
            40501,  // Service is currently busy
            40613   // Database on server is not currently available
        };

        /// <summary>
        /// Determines if an exception is a transient database error that can be retried
        /// </summary>
        /// <param name="exception">The exception to check</param>
        /// <returns>True if the error is transient and can be retried</returns>
        public static bool IsTransientError(Exception exception)
        {
            if (exception is SqlException sqlException)
            {
                return Array.IndexOf(TransientErrorNumbers, sqlException.Number) >= 0;
            }

            // Check for other transient exceptions
            return exception is TimeoutException ||
                   (exception is InvalidOperationException && 
                    exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Executes a database operation with proper error handling and logging
        /// </summary>
        public static async Task<T> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> operation,
            ILoggingProvider loggingProvider,
            string operationName,
            Guid? correlationId = null)
        {
            try
            {
                return await operation();
            }
            catch (SqlException sqlEx) when (IsTransientError(sqlEx))
            {
                var message = $"Transient database error during {operationName}. Error will be retried by Entity Framework: {sqlEx.Message}";
                await loggingProvider.LogWarningAsync(message, "DatabaseConnectionHelper", correlationId);
                throw; // Let EF retry mechanism handle this
            }
            catch (SqlException sqlEx)
            {
                var message = $"Non-transient database error during {operationName}: {sqlEx.Message}";
                await loggingProvider.LogErrorAsync(message, "DatabaseConnectionHelper", sqlEx, correlationId);
                throw;
            }
            catch (DbUpdateException dbEx)
            {
                var message = $"Database update error during {operationName}: {dbEx.Message}";
                await loggingProvider.LogErrorAsync(message, "DatabaseConnectionHelper", dbEx, correlationId);
                throw;
            }
            catch (InvalidOperationException invEx) when (invEx.Message.Contains("timeout"))
            {
                var message = $"Database timeout during {operationName}: {invEx.Message}";
                await loggingProvider.LogWarningAsync(message, "DatabaseConnectionHelper", correlationId);
                throw;
            }
            catch (Exception ex)
            {
                var message = $"Unexpected error during {operationName}: {ex.Message}";
                await loggingProvider.LogErrorAsync(message, "DatabaseConnectionHelper", ex, correlationId);
                throw;
            }
        }

        /// <summary>
        /// Executes a database operation with proper error handling and logging (void return)
        /// </summary>
        public static async Task ExecuteWithErrorHandlingAsync(
            Func<Task> operation,
            ILoggingProvider loggingProvider,
            string operationName,
            Guid? correlationId = null)
        {
            await ExecuteWithErrorHandlingAsync(async () =>
            {
                await operation();
                return true; // Dummy return value
            }, loggingProvider, operationName, correlationId);
        }

        /// <summary>
        /// Tests database connectivity
        /// </summary>
        public static async Task<bool> TestConnectivityAsync(
            DbContext context,
            ILoggingProvider loggingProvider,
            Guid? correlationId = null)
        {
            try
            {
                await context.Database.CanConnectAsync();
                await loggingProvider.LogDebugAsync("Database connectivity test successful", "DatabaseConnectionHelper", correlationId);
                return true;
            }
            catch (Exception ex)
            {
                await loggingProvider.LogErrorAsync("Database connectivity test failed", "DatabaseConnectionHelper", ex, correlationId);
                return false;
            }
        }

        /// <summary>
        /// Gets database connection information for diagnostic purposes
        /// </summary>
        public static async Task<string> GetConnectionInfoAsync(
            DbContext context,
            ILoggingProvider loggingProvider,
            Guid? correlationId = null)
        {
            try
            {
                var connectionString = context.Database.GetConnectionString();
                var builder = new SqlConnectionStringBuilder(connectionString);
                
                return $"Server: {builder.DataSource}, Database: {builder.InitialCatalog}, " +
                       $"Pooling: {builder.Pooling}, Max Pool Size: {builder.MaxPoolSize}, " +
                       $"Min Pool Size: {builder.MinPoolSize}, Connection Timeout: {builder.ConnectTimeout}";
            }
            catch (Exception ex)
            {
                await loggingProvider.LogWarningAsync($"Failed to get connection info: {ex.Message}", "DatabaseConnectionHelper", correlationId);
                return "Connection info unavailable";
            }
        }

        /// <summary>
        /// Validates database schema compatibility
        /// </summary>
        public static async Task<bool> ValidateSchemaAsync(
            DbContext context,
            ILoggingProvider loggingProvider,
            Guid? correlationId = null)
        {
            try
            {
                // Check if database exists and has required tables
                var canConnect = await context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    await loggingProvider.LogErrorAsync("Cannot connect to database", "DatabaseConnectionHelper", null, correlationId);
                    return false;
                }

                // Check for pending migrations
                var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                if (pendingMigrations.Any())
                {
                    await loggingProvider.LogWarningAsync($"Database has pending migrations: {string.Join(", ", pendingMigrations)}", "DatabaseConnectionHelper", correlationId);
                    return false;
                }

                await loggingProvider.LogDebugAsync("Database schema validation successful", "DatabaseConnectionHelper", correlationId);
                return true;
            }
            catch (Exception ex)
            {
                await loggingProvider.LogErrorAsync("Database schema validation failed", "DatabaseConnectionHelper", ex, correlationId);
                return false;
            }
        }
    }
}