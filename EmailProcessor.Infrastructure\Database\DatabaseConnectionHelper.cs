using System;
using System.Data.SqlClient;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace EmailProcessor.Infrastructure.Database
{
    /// <summary>
    /// Helper class for database connection management and error handling
    /// </summary>
    public static class DatabaseConnectionHelper
    {
        /// <summary>
        /// Determines if an exception is a transient database error that can be retried
        /// </summary>
        /// <param name="exception">The exception to check</param>
        /// <returns>True if the error is transient and can be retried</returns>
        public static bool IsTransientError(Exception exception)
        {
            if (exception is SqlException sqlException)
            {
                // Common transient SQL error numbers
                return sqlException.Number switch
                {
                    2 => true,      // Timeout expired
                    20 => true,     // Instance failure
                    64 => true,     // Connection lost
                    233 => true,    // Connection initialization error
                    10053 => true,  // Transport endpoint is not connected
                    10054 => true,  // Connection reset by peer
                    10060 => true,  // Connection timeout
                    40197 => true,  // Service has encountered an error processing your request
                    40501 => true,  // Service is currently busy
                    40613 => true,  // Database on server is not currently available
                    _ => false
                };
            }

            // Check for other transient exceptions
            return exception is TimeoutException ||
                   (exception is InvalidOperationException && 
                    exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Executes a database operation with proper error handling and logging
        /// </summary>
        public static async Task<T> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> operation,
            ILoggingProvider loggingProvider,
            string operationName,
            Guid? correlationId = null)
        {
            try
            {
                return await operation();
            }
            catch (SqlException sqlEx) when (IsTransientError(sqlEx))
            {
                var message = $"Transient database error during {operationName}. Error will be retried by Entity Framework: {sqlEx.Message}";
                await loggingProvider.LogWarningAsync(message, "DatabaseConnectionHelper", correlationId);
                throw; // Let EF retry mechanism handle this
            }
            catch (SqlException sqlEx)
            {
                var message = $"Non-transient database error during {operationName}: {sqlEx.Message}";
                await loggingProvider.LogErrorAsync(message, "DatabaseConnectionHelper", sqlEx, correlationId);
                throw;
            }
            catch (DbUpdateException dbEx)
            {
                var message = $"Database update error during {operationName}: {dbEx.Message}";
                await loggingProvider.LogErrorAsync(message, "DatabaseConnectionHelper", dbEx, correlationId);
                throw;
            }
            catch (InvalidOperationException invEx) when (invEx.Message.Contains("timeout"))
            {
                var message = $"Database timeout during {operationName}: {invEx.Message}";
                await loggingProvider.LogWarningAsync(message, "DatabaseConnectionHelper", correlationId);
                throw;
            }
            catch (Exception ex)
            {
                var message = $"Unexpected error during {operationName}: {ex.Message}";
                await loggingProvider.LogErrorAsync(message, "DatabaseConnectionHelper", ex, correlationId);
                throw;
            }
        }

        /// <summary>
        /// Executes a database operation with proper error handling and logging (void return)
        /// </summary>
        public static async Task ExecuteWithErrorHandlingAsync(
            Func<Task> operation,
            ILoggingProvider loggingProvider,
            string operationName,
            Guid? correlationId = null)
        {
            await ExecuteWithErrorHandlingAsync(async () =>
            {
                await operation();
                return true; // Dummy return value
            }, loggingProvider, operationName, correlationId);
        }

        /// <summary>
        /// Tests database connectivity
        /// </summary>
        public static async Task<bool> TestConnectivityAsync(
            DbContext context,
            ILoggingProvider loggingProvider,
            Guid? correlationId = null)
        {
            try
            {
                await context.Database.CanConnectAsync();
                await loggingProvider.LogDebugAsync("Database connectivity test successful", "DatabaseConnectionHelper", correlationId);
                return true;
            }
            catch (Exception ex)
            {
                await loggingProvider.LogErrorAsync("Database connectivity test failed", "DatabaseConnectionHelper", ex, correlationId);
                return false;
            }
        }
    }
}