using System;
using System.Collections.Generic;
using System.Linq;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Configuration;

namespace EmailProcessor.Infrastructure.Services
{
    /// <summary>
    /// Service for generating enhanced directory structures for email attachments
    /// </summary>
    public class DirectoryStructureService
    {
        private readonly NameSanitizationService _nameSanitizationService;
        private readonly StorageConfiguration _storageConfig;

        public DirectoryStructureService(
            NameSanitizationService nameSanitizationService,
            StorageConfiguration storageConfig)
        {
            _nameSanitizationService = nameSanitizationService ?? throw new ArgumentNullException(nameof(nameSanitizationService));
            _storageConfig = storageConfig ?? throw new ArgumentNullException(nameof(storageConfig));
        }

        /// <summary>
        /// Creates the directory structure path for an email attachment
        /// </summary>
        /// <param name="emailType">Type of email (Sent or Received)</param>
        /// <param name="timestamp">Email timestamp</param>
        /// <param name="senderName">Sender name (for received emails)</param>
        /// <param name="recipientName">Primary recipient name (for sent emails)</param>
        /// <param name="allRecipients">All recipients (for sent emails with multiple recipients)</param>
        /// <returns>Directory structure path</returns>
        public string CreateDirectoryStructure(
            EmailType emailType, 
            DateTime timestamp,
            string senderName = null, 
            string recipientName = null, 
            List<string> allRecipients = null)
        {
            // Create base structure: {Year}\{Month}\{Day}\{EmailType}
            var baseStructure = $"{timestamp.Year}\\{timestamp.Month:D2}\\{timestamp.Day:D2}\\{emailType}";

            // If using basic directory structure, return base structure only
            if (_storageConfig.DirectoryStructureType == DirectoryStructureType.Basic)
            {
                return baseStructure;
            }

            // For enhanced directory structure, add sender/recipient information
            if (emailType == EmailType.Sent)
            {
                return CreateSentEmailStructure(baseStructure, recipientName, allRecipients);
            }
            else // Received
            {
                return CreateReceivedEmailStructure(baseStructure, senderName);
            }
        }

        /// <summary>
        /// Creates directory structure for sent emails
        /// </summary>
        private string CreateSentEmailStructure(string baseStructure, string recipientName, List<string> allRecipients)
        {
            var primaryRecipient = GetPrimaryRecipient(recipientName, allRecipients);
            
            switch (_storageConfig.MultipleRecipientsStrategy)
            {
                case MultipleRecipientsStrategy.PrimaryOnly:
                    return CreatePrimaryOnlyStructure(baseStructure, primaryRecipient);

                case MultipleRecipientsStrategy.Combined:
                    return CreateCombinedRecipientsStructure(baseStructure, allRecipients);

                case MultipleRecipientsStrategy.SeparateFolders:
                    return CreateSeparateFoldersStructure(baseStructure, allRecipients);

                default:
                    return CreatePrimaryOnlyStructure(baseStructure, primaryRecipient);
            }
        }

        /// <summary>
        /// Creates directory structure for received emails
        /// </summary>
        private string CreateReceivedEmailStructure(string baseStructure, string senderName)
        {
            var sanitizedSenderName = _nameSanitizationService.SanitizeName(senderName);
            return $"{baseStructure}\\{sanitizedSenderName}";
        }

        /// <summary>
        /// Creates directory structure using only the primary recipient
        /// </summary>
        private string CreatePrimaryOnlyStructure(string baseStructure, string primaryRecipient)
        {
            var sanitizedName = _nameSanitizationService.SanitizeName(primaryRecipient);
            return $"{baseStructure}\\{sanitizedName}";
        }

        /// <summary>
        /// Creates directory structure combining multiple recipients
        /// </summary>
        private string CreateCombinedRecipientsStructure(string baseStructure, List<string> allRecipients)
        {
            if (allRecipients == null || allRecipients.Count == 0)
            {
                var fallbackName = _nameSanitizationService.SanitizeName("No_Recipients");
                return $"{baseStructure}\\{fallbackName}";
            }

            // Take up to 3 recipients to avoid overly long folder names
            var recipientsToUse = allRecipients.Take(3).ToList();
            var sanitizedRecipients = recipientsToUse
                .Select(r => _nameSanitizationService.SanitizeName(ExtractNameFromEmail(r)))
                .Where(r => !string.IsNullOrWhiteSpace(r))
                .ToList();

            if (sanitizedRecipients.Count == 0)
            {
                var fallbackName = _nameSanitizationService.SanitizeName("Unknown_Recipients");
                return $"{baseStructure}\\{fallbackName}";
            }

            var combinedName = string.Join("_and_", sanitizedRecipients);
            
            // If there were more recipients than we used, add an indicator
            if (allRecipients.Count > 3)
            {
                combinedName += "_and_others";
            }

            // Ensure the combined name doesn't exceed reasonable length
            if (combinedName.Length > _storageConfig.NameSanitization.MaxLength)
            {
                combinedName = combinedName.Substring(0, _storageConfig.NameSanitization.MaxLength - 3) + "...";
            }

            return $"{baseStructure}\\{combinedName}";
        }

        /// <summary>
        /// Creates separate folder structure for multiple recipients
        /// </summary>
        private string CreateSeparateFoldersStructure(string baseStructure, List<string> allRecipients)
        {
            if (allRecipients == null || allRecipients.Count == 0)
            {
                var fallbackName = _nameSanitizationService.SanitizeName("No_Recipients");
                return $"{baseStructure}\\{fallbackName}";
            }

            // For separate folders strategy, we'll use the primary recipient as the main folder
            // and create subfolders for additional recipients if needed
            var primaryRecipient = allRecipients.FirstOrDefault();
            var sanitizedPrimary = _nameSanitizationService.SanitizeName(ExtractNameFromEmail(primaryRecipient));

            if (allRecipients.Count == 1)
            {
                return $"{baseStructure}\\{sanitizedPrimary}";
            }

            // For multiple recipients, create a structure like: Primary\Additional_Recipients
            var additionalRecipients = allRecipients.Skip(1).Take(2).ToList(); // Limit to avoid deep nesting
            var sanitizedAdditional = additionalRecipients
                .Select(r => _nameSanitizationService.SanitizeName(ExtractNameFromEmail(r)))
                .Where(r => !string.IsNullOrWhiteSpace(r))
                .ToList();

            if (sanitizedAdditional.Count > 0)
            {
                var additionalPath = string.Join("_", sanitizedAdditional);
                return $"{baseStructure}\\{sanitizedPrimary}\\{additionalPath}";
            }

            return $"{baseStructure}\\{sanitizedPrimary}";
        }

        /// <summary>
        /// Gets the primary recipient from the available recipient information
        /// </summary>
        private string GetPrimaryRecipient(string recipientName, List<string> allRecipients)
        {
            // If we have a specific recipient name, use it
            if (!string.IsNullOrWhiteSpace(recipientName))
                return recipientName;

            // Otherwise, use the first recipient from the list
            if (allRecipients?.Count > 0)
                return allRecipients.First();

            return "No_Recipients";
        }

        /// <summary>
        /// Extracts name from email address if the recipient is in email format
        /// </summary>
        private string ExtractNameFromEmail(string recipient)
        {
            if (string.IsNullOrWhiteSpace(recipient))
                return "Unknown";

            // If it looks like an email address, extract the local part
            var atIndex = recipient.IndexOf('@');
            if (atIndex > 0)
            {
                return recipient.Substring(0, atIndex);
            }

            // Otherwise, return as-is
            return recipient;
        }

        /// <summary>
        /// Validates that the generated directory structure is valid
        /// </summary>
        public bool IsValidDirectoryStructure(string directoryStructure)
        {
            if (string.IsNullOrWhiteSpace(directoryStructure))
                return false;

            try
            {
                // Check each part of the path
                var parts = directoryStructure.Split('\\');
                foreach (var part in parts)
                {
                    if (!_nameSanitizationService.IsValidFileName(part))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
