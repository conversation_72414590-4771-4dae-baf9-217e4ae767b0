{"format": 1, "restore": {"C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Infrastructure\\EmailProcessor.Infrastructure.csproj": {}}, "projects": {"C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Domain\\EmailProcessor.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Domain\\EmailProcessor.Domain.csproj", "projectName": "EmailProcessor.Domain", "projectPath": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Domain\\EmailProcessor.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100-preview.7.24407.12/PortableRuntimeIdentifierGraph.json"}}}, "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Infrastructure\\EmailProcessor.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Infrastructure\\EmailProcessor.Infrastructure.csproj", "projectName": "EmailProcessor.Infrastructure", "projectPath": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Infrastructure\\EmailProcessor.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Domain\\EmailProcessor.Domain.csproj": {"projectPath": "C:\\projects\\Outlooks\\Emaily-V01\\EmailProcessor.Domain\\EmailProcessor.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100-preview.7.24407.12/PortableRuntimeIdentifierGraph.json"}}}}}