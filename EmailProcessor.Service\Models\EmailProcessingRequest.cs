using System.Text.Json.Serialization;

namespace EmailProcessor.Service.Models
{
    /// <summary>
    /// Request model for email processing from VSTO add-in
    /// </summary>
    public class EmailProcessingRequest
    {
        [JsonPropertyName("messageType")]
        public string MessageType { get; set; } = "EmailProcessingRequest";

        [JsonPropertyName("correlationId")]
        public Guid CorrelationId { get; set; } = Guid.NewGuid();

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("data")]
        public EmailProcessingData Data { get; set; } = new EmailProcessingData();
    }

    /// <summary>
    /// Email processing data containing email and attachment information
    /// </summary>
    public class EmailProcessingData
    {
        [JsonPropertyName("email")]
        public EmailData Email { get; set; } = new EmailData();

        [JsonPropertyName("attachments")]
        public List<AttachmentData> Attachments { get; set; } = new List<AttachmentData>();
    }

    /// <summary>
    /// Email data structure
    /// </summary>
    public class EmailData
    {
        [JsonPropertyName("subject")]
        public string Subject { get; set; } = string.Empty;

        [JsonPropertyName("senderName")]
        public string SenderName { get; set; } = string.Empty;

        [JsonPropertyName("senderEmail")]
        public string SenderEmail { get; set; } = string.Empty;

        [JsonPropertyName("recipientTo")]
        public List<string> RecipientTo { get; set; } = new List<string>();

        [JsonPropertyName("recipientCC")]
        public List<string> RecipientCC { get; set; } = new List<string>();

        [JsonPropertyName("emailType")]
        public string EmailType { get; set; } = "Received"; // "Received" or "Sent"

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("outlookMessageId")]
        public string OutlookMessageId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Attachment data structure
    /// </summary>
    public class AttachmentData
    {
        [JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        [JsonPropertyName("originalFileName")]
        public string OriginalFileName { get; set; } = string.Empty;

        [JsonPropertyName("contentType")]
        public string ContentType { get; set; } = string.Empty;

        [JsonPropertyName("fileExtension")]
        public string FileExtension { get; set; } = string.Empty;

        [JsonPropertyName("fileSize")]
        public long FileSize { get; set; }

        [JsonPropertyName("fileData")]
        public string FileData { get; set; } = string.Empty; // Base64 encoded file data
    }

    /// <summary>
    /// Response model for email processing results
    /// </summary>
    public class EmailProcessingResponse
    {
        [JsonPropertyName("messageType")]
        public string MessageType { get; set; } = "EmailProcessingResponse";

        [JsonPropertyName("correlationId")]
        public Guid CorrelationId { get; set; }

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("emailId")]
        public long? EmailId { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("errorDetails")]
        public string? ErrorDetails { get; set; }

        [JsonPropertyName("processingResults")]
        public List<AttachmentProcessingResult> ProcessingResults { get; set; } = new List<AttachmentProcessingResult>();
    }

    /// <summary>
    /// Individual attachment processing result
    /// </summary>
    public class AttachmentProcessingResult
    {
        [JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        [JsonPropertyName("attachmentId")]
        public long? AttachmentId { get; set; }

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("localStoragePath")]
        public string? LocalStoragePath { get; set; }

        [JsonPropertyName("uncStoragePath")]
        public string? UncStoragePath { get; set; }

        [JsonPropertyName("errorMessage")]
        public string? ErrorMessage { get; set; }
    }
} 