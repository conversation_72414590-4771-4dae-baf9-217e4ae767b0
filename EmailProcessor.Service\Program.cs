using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Serilog;
using EmailProcessor.Infrastructure.Configuration;
using EmailProcessor.Infrastructure.Logging;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using EmailProcessor.Infrastructure.Database.Context;
using EmailProcessor.Infrastructure.Database.Repositories;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Service.Services;
using EmailProcessor.Service.Communication;
using EmailProcessor.Service.Configuration;
using EmailProcessor.Infrastructure.Services;
using Microsoft.Extensions.Options;

namespace EmailProcessor.Service
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"}.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables()
                    .AddCommandLine(args)
                    .Build();

                // Configure Serilog
                Log.Logger = new LoggerConfiguration()
                    .ReadFrom.Configuration(configuration)
                    .Enrich.FromLogContext()
                    .Enrich.WithProperty("Application", "EmailProcessor.Service")
                    .Enrich.WithProperty("MachineName", Environment.MachineName)
                    .Enrich.WithProperty("ProcessId", Environment.ProcessId)
                    .CreateLogger();

                Log.Information("Starting Email Processor Service...");

                // Create host builder
                var host = CreateHostBuilder(args, configuration).Build();

                // Run the service
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Email Processor Service terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args, IConfiguration configuration) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService(options =>
                {
                    options.ServiceName = "EmailProcessorService";
                })
                .ConfigureServices((hostContext, services) =>
                {
                    // Register configuration
                    services.Configure<EmailProcessorConfiguration>(
                        configuration.GetSection("EmailProcessor"));
                    
                    // Register service configuration
                    services.Configure<ServiceConfiguration>(configuration);

                    // Register logging
                    services.AddSingleton<ILoggingProvider>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Logging").Get<LoggingConfiguration>();
                        var serilogConfig = new SerilogLoggingConfiguration
                        {
                            MinimumLevel = config?.LogLevel ?? LogLevel.Information,
                            EnableConsoleLogging = config?.EnableConsoleLogging ?? false,
                            EnableFileLogging = config?.EnableFileLogging ?? true,
                            LogFilePath = config?.LogFilePath ?? Path.Combine("Logs", "EmailProcessor-.log"),
                            UseStructuredLogging = config?.UseStructuredLogging ?? false,
                            RetainedFileCount = 30,
                            FileSizeLimitBytes = config?.MaxLogFileSizeBytes ?? 100 * 1024 * 1024,
                            FlushIntervalSeconds = config?.FlushIntervalSeconds ?? 5
                        };
                        return new SerilogLoggingProvider(serilogConfig);
                    });

                    // Register database context with scoped lifetime for thread safety
                    services.AddDbContext<EmailProcessorContext>((serviceProvider, options) =>
                    {
                        var dbConfig = configuration.GetSection("EmailProcessor:Database").Get<DatabaseConfiguration>() ?? new DatabaseConfiguration();
                        
                        // Use enhanced connection string with connection pooling
                        var connectionString = dbConfig.GetEnhancedConnectionString();
                        
                        options.UseSqlServer(connectionString, sqlOptions =>
                        {
                            // Configure command timeout
                            sqlOptions.CommandTimeout(dbConfig.CommandTimeout);
                            
                            // Configure retry policy for transient failures
                            if (dbConfig.EnableRetryOnFailure)
                            {
                                sqlOptions.EnableRetryOnFailure(
                                    maxRetryCount: dbConfig.MaxRetryAttempts,
                                    maxRetryDelay: TimeSpan.FromSeconds(dbConfig.RetryDelaySeconds),
                                    errorNumbersToAdd: new[] { 2, 20, 64, 233, 10053, 10054, 10060, 40197, 40501, 40613 });
                            }
                            
                            // Enable connection resiliency
                            sqlOptions.EnableServiceProviderCaching();
                            sqlOptions.EnableSensitiveDataLogging(false);
                        });
                        
                        // Configure DbContext options
                        options.EnableSensitiveDataLogging(false);
                        options.EnableServiceProviderCaching();
                        options.ConfigureWarnings(warnings =>
                        {
                            warnings.Default(WarningBehavior.Log);
                        });
                    }, ServiceLifetime.Scoped);

                    // Register repositories with scoped lifetime to match DbContext
                    services.AddScoped<IEmailRepository, EmailRepository>();
                    services.AddScoped<IAttachmentRepository, AttachmentRepository>();
                    services.AddScoped<IProcessingLogRepository, ProcessingLogRepository>();
                    services.AddScoped<IConfigurationRepository, ConfigurationRepository>();

                    // Register storage providers
                    services.AddSingleton<IStorageProvider>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Storage").Get<StorageConfiguration>();
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();
                        return new LocalStorageProvider(
                            config?.LocalBasePath ?? @"C:\EmailAttachments",
                            loggingProvider);
                    });

                    // Register UNC storage provider conditionally
                    services.AddSingleton<IStorageProvider>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Storage").Get<StorageConfiguration>();
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();
                        
                        // Check if UNC path is configured and valid
                        var uncPath = config?.UncBasePath;
                        if (string.IsNullOrWhiteSpace(uncPath) || !uncPath.StartsWith(@"\\"))
                        {
                            loggingProvider.LogWarning("UNC storage provider not configured or invalid UNC path. UNC storage will be disabled.");
                            return new NullStorageProvider(loggingProvider);
                        }

                        try
                        {
                            return new UncStorageProvider(
                                uncPath,
                                loggingProvider,
                                config?.UncUsername,
                                config?.UncPassword);
                        }
                        catch (Exception ex)
                        {
                            var errorMessage = $"Failed to initialize UNC storage provider: {ex.Message}";
                            loggingProvider.LogError(errorMessage);
                            
                            // If UNC storage is required, throw the exception
                            if (config?.UncStorageRequired == true)
                            {
                                throw new InvalidOperationException($"{errorMessage}. UNC storage is required but not available.", ex);
                            }
                            
                            // Otherwise, log warning and return null provider
                            loggingProvider.LogWarning("UNC storage will be disabled due to initialization failure.");
                            return new NullStorageProvider(loggingProvider);
                        }
                    });

                    // Register dual storage service
                    services.AddSingleton<DualStorageService>(provider =>
                    {
                        var storageProviders = provider.GetServices<IStorageProvider>().ToList();
                        var localProvider = storageProviders.FirstOrDefault(p => p.StorageType == StorageType.Local);
                        var uncProvider = storageProviders.FirstOrDefault(p => p.StorageType == StorageType.Unc);
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();

                        if (localProvider == null)
                        {
                            throw new InvalidOperationException("Local storage provider must be registered");
                        }

                        // If UNC provider is not available, create a dual storage service with only local storage
                        if (uncProvider == null || uncProvider is NullStorageProvider)
                        {
                            loggingProvider.LogWarning("UNC storage provider not available. Using local storage only.");
                            return new DualStorageService(localProvider, new NullStorageProvider(loggingProvider), loggingProvider);
                        }

                        return new DualStorageService(localProvider, uncProvider, loggingProvider);
                    });

                    // Register configuration service
                    services.AddSingleton<IConfigurationService, ConfigurationService>();

                    // Register log management service
                    services.AddScoped<ILogManagementService, LogManagementService>();

                    // Register communication services
                    services.AddSingleton<NamedPipeServer>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Communication").Get<CommunicationConfiguration>();
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();
                        return new NamedPipeServer(
                            config?.NamedPipeName ?? "EmailProcessorPipe",
                            loggingProvider);
                    });

                    // Register processing services with scoped lifetime to match repository dependencies
                    services.AddScoped<EmailProcessingService>();
                    services.AddScoped<AttachmentHandlerService>();

                    // Register enhanced directory structure services
                    services.AddSingleton<NameSanitizationService>(provider =>
                    {
                        var config = provider.GetRequiredService<IOptions<EmailProcessorConfiguration>>().Value;
                        return new NameSanitizationService(config.Storage.NameSanitization);
                    });
                    
                    // Register StorageConfiguration as a service
                    services.AddSingleton<StorageConfiguration>(provider =>
                    {
                        var config = provider.GetRequiredService<IOptions<EmailProcessorConfiguration>>().Value;
                        return config.Storage;
                    });
                    
                    services.AddSingleton<DirectoryStructureService>();

                    // Register the main service
                    services.AddHostedService<EmailProcessorHostedService>();
                })
                .UseSerilog();
    }
} 