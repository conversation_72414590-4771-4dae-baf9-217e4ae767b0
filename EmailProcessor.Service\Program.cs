using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Serilog;
using EmailProcessor.Infrastructure.Configuration;
using EmailProcessor.Infrastructure.Logging;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using EmailProcessor.Infrastructure.Database.Context;
using EmailProcessor.Infrastructure.Database.Repositories;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Service.Services;
using EmailProcessor.Service.Communication;
using EmailProcessor.Service.Configuration;

namespace EmailProcessor.Service
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"}.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables()
                    .AddCommandLine(args)
                    .Build();

                // Configure Serilog
                Log.Logger = new LoggerConfiguration()
                    .ReadFrom.Configuration(configuration)
                    .Enrich.FromLogContext()
                    .Enrich.WithProperty("Application", "EmailProcessor.Service")
                    .Enrich.WithProperty("MachineName", Environment.MachineName)
                    .Enrich.WithProperty("ProcessId", Environment.ProcessId)
                    .CreateLogger();

                Log.Information("Starting Email Processor Service...");

                // Create host builder
                var host = CreateHostBuilder(args, configuration).Build();

                // Run the service
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Email Processor Service terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args, IConfiguration configuration) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService(options =>
                {
                    options.ServiceName = "EmailProcessorService";
                })
                .ConfigureServices((hostContext, services) =>
                {
                    // Register configuration
                    services.Configure<EmailProcessorConfiguration>(
                        configuration.GetSection("EmailProcessor"));
                    
                    // Register service configuration
                    services.Configure<ServiceConfiguration>(configuration);

                    // Register logging
                    services.AddSingleton<ILoggingProvider>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Logging").Get<LoggingConfiguration>();
                        var serilogConfig = new SerilogLoggingConfiguration
                        {
                            MinimumLevel = config?.LogLevel ?? LogLevel.Information,
                            EnableConsoleLogging = config?.EnableConsoleLogging ?? false,
                            EnableFileLogging = config?.EnableFileLogging ?? true,
                            LogFilePath = config?.LogFilePath ?? Path.Combine("Logs", "EmailProcessor-.log"),
                            UseStructuredLogging = config?.UseStructuredLogging ?? false,
                            RetainedFileCount = 30,
                            FileSizeLimitBytes = config?.MaxLogFileSizeBytes ?? 100 * 1024 * 1024,
                            FlushIntervalSeconds = config?.FlushIntervalSeconds ?? 5
                        };
                        return new SerilogLoggingProvider(serilogConfig);
                    });

                    // Register database context
                    services.AddDbContext<EmailProcessorContext>(options =>
                    {
                        var dbConfig = configuration.GetSection("EmailProcessor:Database").Get<DatabaseConfiguration>();
                        options.UseSqlServer(
                            dbConfig?.ConnectionString ?? "Server=localhost;Database=EmailProcessor;Trusted_Connection=true;",
                            sqlOptions =>
                            {
                                sqlOptions.CommandTimeout(dbConfig?.CommandTimeout ?? 30);
                                if (dbConfig?.EnableRetryOnFailure == true)
                                {
                                    sqlOptions.EnableRetryOnFailure(
                                        maxRetryCount: dbConfig.MaxRetryAttempts,
                                        maxRetryDelay: TimeSpan.FromSeconds(dbConfig.RetryDelaySeconds),
                                        errorNumbersToAdd: null);
                                }
                            });
                    });

                    // Register repositories
                    services.AddScoped<IEmailRepository, EmailRepository>();
                    services.AddScoped<IAttachmentRepository, AttachmentRepository>();
                    services.AddScoped<IProcessingLogRepository, ProcessingLogRepository>();
                    services.AddScoped<IConfigurationRepository, ConfigurationRepository>();

                    // Register storage providers
                    services.AddSingleton<IStorageProvider>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Storage").Get<StorageConfiguration>();
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();
                        return new LocalStorageProvider(
                            config?.LocalBasePath ?? @"C:\EmailAttachments",
                            loggingProvider);
                    });

                    services.AddSingleton<IStorageProvider>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Storage").Get<StorageConfiguration>();
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();
                        return new UncStorageProvider(
                            config?.UncBasePath ?? @"\\server\share\EmailAttachments",
                            loggingProvider,
                            config?.UncUsername,
                            config?.UncPassword);
                    });

                    // Register dual storage service
                    services.AddSingleton<DualStorageService>(provider =>
                    {
                        var storageProviders = provider.GetServices<IStorageProvider>().ToList();
                        var localProvider = storageProviders.FirstOrDefault(p => p.StorageType == StorageType.Local);
                        var uncProvider = storageProviders.FirstOrDefault(p => p.StorageType == StorageType.Unc);
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();

                        if (localProvider == null || uncProvider == null)
                        {
                            throw new InvalidOperationException("Both Local and UNC storage providers must be registered");
                        }

                        return new DualStorageService(localProvider, uncProvider, loggingProvider);
                    });

                    // Register configuration service
                    services.AddScoped<IConfigurationService, ConfigurationService>();

                    // Register log management service
                    services.AddScoped<ILogManagementService, LogManagementService>();

                    // Register communication services
                    services.AddSingleton<NamedPipeServer>(provider =>
                    {
                        var config = configuration.GetSection("EmailProcessor:Communication").Get<CommunicationConfiguration>();
                        var loggingProvider = provider.GetRequiredService<ILoggingProvider>();
                        return new NamedPipeServer(
                            config?.NamedPipeName ?? "EmailProcessorPipe",
                            loggingProvider);
                    });

                    // Register processing services
                    services.AddScoped<EmailProcessingService>();
                    services.AddScoped<AttachmentHandlerService>();

                    // Register the main service
                    services.AddHostedService<EmailProcessorHostedService>();
                })
                .UseSerilog();
    }
} 