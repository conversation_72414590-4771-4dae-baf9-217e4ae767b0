﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\EmailProcessor.Domain\EmailProcessor.Domain.csproj" />
    <ProjectReference Include="..\EmailProcessor.Infrastructure\EmailProcessor.Infrastructure.csproj" />
    <ProjectReference Include="..\EmailProcessor.Shared\EmailProcessor.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
    <PackageReference Include="Serilog" Version="4.3.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
