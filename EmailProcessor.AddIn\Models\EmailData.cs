using System;
using System.Collections.Generic;

namespace EmailProcessor.AddIn.Models
{
    /// <summary>
    /// Email data extracted from Outlook MailItem
    /// </summary>
    public class EmailData
    {
        public string Subject { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
        public string SenderEmail { get; set; } = string.Empty;
        public List<string> RecipientTo { get; set; } = new List<string>();
        public List<string> RecipientCC { get; set; } = new List<string>();
        public string EmailType { get; set; } = "Received"; // "Received" or "Sent"
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string OutlookMessageId { get; set; } = string.Empty;
        public string EntryId { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public bool HasAttachments { get; set; }
        public int AttachmentCount { get; set; }
    }
} 