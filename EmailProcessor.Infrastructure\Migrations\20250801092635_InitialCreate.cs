﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmailProcessor.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Configuration",
                columns: table => new
                {
                    ConfigId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ConfigKey = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ConfigValue = table.Column<string>(type: "nvarchar(max)", maxLength: -1, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsEncrypted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Configuration", x => x.ConfigId);
                });

            migrationBuilder.CreateTable(
                name: "Emails",
                columns: table => new
                {
                    EmailId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Subject = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    SenderName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    SenderEmail = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    EmailType = table.Column<int>(type: "int", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OutlookMessageId = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    ProcessingStatus = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PrimaryRecipientName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    PrimaryRecipientEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    AllRecipientsJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AllRecipients = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Emails", x => x.EmailId);
                });

            migrationBuilder.CreateTable(
                name: "Attachments",
                columns: table => new
                {
                    AttachmentId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EmailId = table.Column<long>(type: "bigint", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    OriginalFileName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    ContentType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    FileExtension = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    LocalStoragePath = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    UncStoragePath = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    LocalProcessingStatus = table.Column<int>(type: "int", nullable: false),
                    UncProcessingStatus = table.Column<int>(type: "int", nullable: false),
                    LocalErrorMessage = table.Column<string>(type: "nvarchar(max)", maxLength: -1, nullable: true),
                    UncErrorMessage = table.Column<string>(type: "nvarchar(max)", maxLength: -1, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DirectoryPath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    EmailId1 = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Attachments", x => x.AttachmentId);
                    table.ForeignKey(
                        name: "FK_Attachments_Emails_EmailId",
                        column: x => x.EmailId,
                        principalTable: "Emails",
                        principalColumn: "EmailId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Attachments_Emails_EmailId1",
                        column: x => x.EmailId1,
                        principalTable: "Emails",
                        principalColumn: "EmailId");
                });

            migrationBuilder.CreateTable(
                name: "Emails_RecipientCC",
                columns: table => new
                {
                    EmailId = table.Column<long>(type: "bigint", nullable: false),
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RecipientCC = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Emails_RecipientCC", x => new { x.EmailId, x.Id });
                    table.ForeignKey(
                        name: "FK_Emails_RecipientCC_Emails_EmailId",
                        column: x => x.EmailId,
                        principalTable: "Emails",
                        principalColumn: "EmailId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Emails_RecipientTo",
                columns: table => new
                {
                    EmailId = table.Column<long>(type: "bigint", nullable: false),
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RecipientTo = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Emails_RecipientTo", x => new { x.EmailId, x.Id });
                    table.ForeignKey(
                        name: "FK_Emails_RecipientTo_Emails_EmailId",
                        column: x => x.EmailId,
                        principalTable: "Emails",
                        principalColumn: "EmailId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProcessingLogs",
                columns: table => new
                {
                    LogId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EmailId = table.Column<long>(type: "bigint", nullable: true),
                    AttachmentId = table.Column<long>(type: "bigint", nullable: true),
                    LogLevel = table.Column<int>(type: "int", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(max)", maxLength: -1, nullable: false),
                    ExceptionDetails = table.Column<string>(type: "nvarchar(max)", maxLength: -1, nullable: true),
                    SourceComponent = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CorrelationId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    AttachmentId1 = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProcessingLogs", x => x.LogId);
                    table.ForeignKey(
                        name: "FK_ProcessingLogs_Attachments_AttachmentId",
                        column: x => x.AttachmentId,
                        principalTable: "Attachments",
                        principalColumn: "AttachmentId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProcessingLogs_Attachments_AttachmentId1",
                        column: x => x.AttachmentId1,
                        principalTable: "Attachments",
                        principalColumn: "AttachmentId");
                    table.ForeignKey(
                        name: "FK_ProcessingLogs_Emails_EmailId",
                        column: x => x.EmailId,
                        principalTable: "Emails",
                        principalColumn: "EmailId",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_DirectoryPath",
                table: "Attachments",
                column: "DirectoryPath");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_EmailId",
                table: "Attachments",
                column: "EmailId");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_EmailId1",
                table: "Attachments",
                column: "EmailId1");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_FileExtension",
                table: "Attachments",
                column: "FileExtension");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_LocalProcessingStatus",
                table: "Attachments",
                column: "LocalProcessingStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_UncProcessingStatus",
                table: "Attachments",
                column: "UncProcessingStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Configuration_ConfigKey",
                table: "Configuration",
                column: "ConfigKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Emails_EmailType",
                table: "Emails",
                column: "EmailType");

            migrationBuilder.CreateIndex(
                name: "IX_Emails_OutlookMessageId",
                table: "Emails",
                column: "OutlookMessageId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Emails_PrimaryRecipientName",
                table: "Emails",
                column: "PrimaryRecipientName");

            migrationBuilder.CreateIndex(
                name: "IX_Emails_ProcessingStatus",
                table: "Emails",
                column: "ProcessingStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Emails_SenderName",
                table: "Emails",
                column: "SenderName");

            migrationBuilder.CreateIndex(
                name: "IX_Emails_Timestamp",
                table: "Emails",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessingLogs_AttachmentId",
                table: "ProcessingLogs",
                column: "AttachmentId");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessingLogs_AttachmentId1",
                table: "ProcessingLogs",
                column: "AttachmentId1");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessingLogs_CorrelationId",
                table: "ProcessingLogs",
                column: "CorrelationId");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessingLogs_CreatedDate",
                table: "ProcessingLogs",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessingLogs_EmailId",
                table: "ProcessingLogs",
                column: "EmailId");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessingLogs_LogLevel",
                table: "ProcessingLogs",
                column: "LogLevel");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Configuration");

            migrationBuilder.DropTable(
                name: "Emails_RecipientCC");

            migrationBuilder.DropTable(
                name: "Emails_RecipientTo");

            migrationBuilder.DropTable(
                name: "ProcessingLogs");

            migrationBuilder.DropTable(
                name: "Attachments");

            migrationBuilder.DropTable(
                name: "Emails");
        }
    }
}
