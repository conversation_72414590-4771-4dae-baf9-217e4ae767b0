# Email Attachment Processor - Technical Specifications Summary

## 1. System Architecture Overview

### 1.1 Clean Architecture Layers

- **Presentation Layer**: VSTO Add-in (.NET Framework 4.8)
- **Application Layer**: Windows Service (.NET 8)
- **Domain Layer**: Core Business Logic (.NET 8)
- **Infrastructure Layer**: External Concerns (.NET 8)

### 1.2 Key Components

- **VSTO Add-in**: Outlook event monitoring and communication
- **Windows Service**: Core processing orchestration
- **Named Pipes**: Inter-process communication
- **Dual Storage**: Local + UNC network storage
- **SQL Server**: Metadata storage with Entity Framework

## 2. Database Schema

### 2.1 Core Tables

```sql
-- Emails Table
CREATE TABLE Emails (
    EmailId BIGINT IDENTITY(1,1) PRIMARY KEY,
    Subject NVARCHAR(500) NOT NULL,
    SenderName NVARCHAR(255),
    SenderEmail NVARCHAR(255) NOT NULL,
    RecipientTo NVARCHAR(MAX), -- JSON array
    RecipientCC NVARCHAR(MAX), -- JSON array
    EmailType TINYINT NOT NULL, -- 1=Received, 2=Sent
    Timestamp DATETIME2 NOT NULL,
    OutlookMessageId NVARCHAR(255),
    ProcessingStatus TINYINT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Attachments Table
CREATE TABLE Attachments (
    AttachmentId BIGINT IDENTITY(1,1) PRIMARY KEY,
    EmailId BIGINT NOT NULL,
    FileName NVARCHAR(500) NOT NULL,
    ContentType NVARCHAR(255),
    FileExtension NVARCHAR(50),
    FileSize BIGINT NOT NULL,
    LocalStoragePath NVARCHAR(1000) NOT NULL,
    UncStoragePath NVARCHAR(1000) NOT NULL,
    LocalProcessingStatus TINYINT NOT NULL DEFAULT 1,
    UncProcessingStatus TINYINT NOT NULL DEFAULT 1,
    LocalErrorMessage NVARCHAR(MAX),
    UncErrorMessage NVARCHAR(MAX),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (EmailId) REFERENCES Emails(EmailId) ON DELETE CASCADE
);

-- ProcessingLogs Table
CREATE TABLE ProcessingLogs (
    LogId BIGINT IDENTITY(1,1) PRIMARY KEY,
    EmailId BIGINT NULL,
    AttachmentId BIGINT NULL,
    LogLevel TINYINT NOT NULL,
    Message NVARCHAR(MAX) NOT NULL,
    ExceptionDetails NVARCHAR(MAX),
    SourceComponent NVARCHAR(100) NOT NULL,
    CorrelationId UNIQUEIDENTIFIER,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (EmailId) REFERENCES Emails(EmailId) ON DELETE CASCADE,
    FOREIGN KEY (AttachmentId) REFERENCES Attachments(AttachmentId) ON DELETE CASCADE
);
```

## 3. Communication Protocol

### 3.1 Named Pipe Message Format

```json
{
	"messageType": "EmailProcessingRequest",
	"correlationId": "guid",
	"timestamp": "2025-01-27T10:30:00Z",
	"data": {
		"email": {
			"subject": "string",
			"senderName": "string",
			"senderEmail": "string",
			"recipientTo": "string[]",
			"recipientCC": "string[]",
			"emailType": "Received|Sent",
			"timestamp": "2025-01-27T10:30:00Z",
			"outlookMessageId": "string"
		},
		"attachments": [
			{
				"fileName": "string",
				"contentType": "string",
				"fileExtension": "string",
				"fileSize": "long",
				"fileData": "base64"
			}
		]
	}
}
```

## 4. Configuration Structure

### 4.1 appsettings.json

```json
{
	"Database": {
		"ConnectionString": "Server=localhost;Database=EmailProcessor;Trusted_Connection=true;",
		"CommandTimeout": 30,
		"EnableRetryOnFailure": true
	},
	"Storage": {
		"LocalBasePath": "C:\\EmailAttachments",
		"UncBasePath": "\\\\server\\share\\EmailAttachments",
		"MaxFileSize": 104857600,
		"CreateDirectories": true,
		"AllowedExtensions": [".pdf", ".doc", ".docx", ".xls", ".xlsx"]
	},
	"Processing": {
		"RetryCount": 3,
		"RetryDelaySeconds": 5,
		"MaxConcurrentProcessing": 10,
		"ProcessingTimeoutSeconds": 30
	},
	"Logging": {
		"LogLevel": "Information",
		"LogFilePath": "C:\\Logs\\EmailProcessor\\",
		"LogFileRetentionDays": 30,
		"EnableConsoleLogging": false
	},
	"Communication": {
		"NamedPipeName": "EmailProcessorPipe",
		"ConnectionTimeoutSeconds": 10,
		"MaxMessageSize": 104857600
	}
}
```

## 5. Key Dependencies

### 5.1 NuGet Packages

```xml
<!-- Infrastructure -->
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />

<!-- Service Layer -->
<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />

<!-- Logging -->
<PackageReference Include="Serilog" Version="3.1.1" />
<PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

<!-- Testing -->
<PackageReference Include="Moq" Version="4.20.69" />
<PackageReference Include="FluentAssertions" Version="6.12.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
```

## 6. Performance Requirements

### 6.1 Processing Targets

- **Processing Time**: < 2 seconds for typical emails
- **Database Response**: < 1 second for queries
- **Concurrent Processing**: Support for multiple emails simultaneously
- **File Size Support**: Up to 100MB attachments (configurable)

### 6.2 Success Metrics

- **Processing Success Rate**: > 99%
- **System Uptime**: > 99%
- **Data Accuracy**: 100%
- **Error Rate**: < 1%

## 7. Error Handling Strategy

### 7.1 Retry Mechanisms

- **Exponential Backoff**: Configurable retry with increasing delays
- **Retry Count**: Configurable (default: 3 attempts)
- **Timeout Handling**: Configurable timeouts for all operations
- **Graceful Degradation**: Continue processing if one storage fails

### 7.2 Error Categories

- **Transient Errors**: Network issues, temporary file locks
- **Permanent Errors**: Invalid file types, storage full
- **System Errors**: Service unavailable, database connection issues
- **VSTO Errors**: Outlook process issues, add-in failures

## 8. Security Considerations

### 8.1 Data Security

- **Encryption**: Sensitive configuration data encryption
- **Access Control**: File system and database permissions
- **Audit Trail**: Comprehensive logging for security events
- **Input Validation**: All input data validation and sanitization

### 8.2 VSTO Security

- **Code Signing**: Proper VSTO add-in signing
- **Registry Security**: Secure registry access for VSTO
- **Office Security**: Configuration for Office security policies
- **Process Isolation**: Proper COM object disposal

## 9. Testing Strategy

### 9.1 Test Coverage Targets

- **Unit Tests**: > 80% code coverage
- **Integration Tests**: All component interactions
- **End-to-End Tests**: Complete workflow validation
- **Performance Tests**: Load and stress testing

### 9.2 Test Categories

- **Domain Tests**: Business logic validation
- **Infrastructure Tests**: Database and storage operations
- **Service Tests**: Windows Service functionality
- **VSTO Tests**: Outlook integration and communication

## 10. Deployment Requirements

### 10.1 Prerequisites

- .NET Framework 4.8 Runtime
- .NET 8 Runtime
- SQL Server (Local or Network)
- Microsoft Outlook
- Windows Named Pipes support
- File system access (Local + UNC)

### 10.2 Installation Steps

1. Install .NET runtimes
2. Deploy Windows Service
3. Configure database and run migrations
4. Configure storage paths and permissions
5. Deploy VSTO Add-in
6. Configure Outlook security settings
7. Test complete workflow

## 11. Monitoring and Maintenance

### 11.1 Health Monitoring

- Windows Service status
- Database connectivity
- Storage system availability
- Processing queue status
- Error rates and types

### 11.2 Performance Monitoring

- Processing latency
- Database response times
- File I/O performance
- Memory usage
- CPU utilization

### 11.3 Maintenance Tasks

- Database index maintenance
- Log file rotation and cleanup
- Storage space monitoring
- Performance optimization
- Security updates

## 12. Risk Mitigation

### 12.1 Technical Risks

- **VSTO Stability**: Comprehensive error handling and minimal VSTO code
- **Performance**: Optimization, caching, and parallel processing
- **Storage Failures**: Dual storage strategy with failover
- **Database Issues**: Connection pooling and retry mechanisms

### 12.2 Operational Risks

- **Deployment Complexity**: Automated scripts and clear documentation
- **Configuration Errors**: Validation and default values
- **Maintenance Difficulty**: Comprehensive logging and monitoring

This technical specification provides the essential details needed to implement
the Email Attachment Processor system according to the PRD requirements while
maintaining Clean Architecture principles and ensuring robust, scalable, and
maintainable code.
