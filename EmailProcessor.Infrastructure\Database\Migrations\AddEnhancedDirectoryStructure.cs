using Microsoft.EntityFrameworkCore.Migrations;

namespace EmailProcessor.Infrastructure.Database.Migrations
{
    /// <summary>
    /// Migration to add enhanced directory structure support
    /// </summary>
    public partial class AddEnhancedDirectoryStructure : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add new columns to Email table
            migrationBuilder.AddColumn<string>(
                name: "PrimaryRecipientName",
                table: "Emails",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrimaryRecipientEmail",
                table: "Emails",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AllRecipients<PERSON><PERSON>",
                table: "Emails",
                type: "nvarchar(max)",
                nullable: true);

            // Add new column to Attachment table
            migrationBuilder.AddColumn<string>(
                name: "DirectoryPath",
                table: "Attachments",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            // Create indexes for performance
            migrationBuilder.CreateIndex(
                name: "IX_Emails_PrimaryRecipientName",
                table: "Emails",
                column: "PrimaryRecipientName");

            migrationBuilder.CreateIndex(
                name: "IX_Emails_SenderName",
                table: "Emails",
                column: "SenderName");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_DirectoryPath",
                table: "Attachments",
                column: "DirectoryPath");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove indexes
            migrationBuilder.DropIndex(
                name: "IX_Emails_PrimaryRecipientName",
                table: "Emails");

            migrationBuilder.DropIndex(
                name: "IX_Emails_SenderName",
                table: "Emails");

            migrationBuilder.DropIndex(
                name: "IX_Attachments_DirectoryPath",
                table: "Attachments");

            // Remove columns
            migrationBuilder.DropColumn(
                name: "PrimaryRecipientName",
                table: "Emails");

            migrationBuilder.DropColumn(
                name: "PrimaryRecipientEmail",
                table: "Emails");

            migrationBuilder.DropColumn(
                name: "AllRecipientsJson",
                table: "Emails");

            migrationBuilder.DropColumn(
                name: "DirectoryPath",
                table: "Attachments");
        }
    }
}
