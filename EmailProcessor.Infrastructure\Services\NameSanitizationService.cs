using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using EmailProcessor.Infrastructure.Configuration;

namespace EmailProcessor.Infrastructure.Services
{
    /// <summary>
    /// Service for sanitizing sender/recipient names for file system compatibility
    /// </summary>
    public class NameSanitizationService
    {
        private readonly NameSanitizationSettings _settings;
        private static readonly char[] InvalidFileNameChars = Path.GetInvalidFileNameChars();
        private static readonly char[] InvalidPathChars = Path.GetInvalidPathChars();

        public NameSanitizationService(NameSanitizationSettings settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        /// <summary>
        /// Sanitizes a name for use in file system paths
        /// </summary>
        /// <param name="name">The name to sanitize</param>
        /// <param name="email">Optional email address to use as fallback</param>
        /// <returns>Sanitized name safe for file system use</returns>
        public string SanitizeName(string name, string email = null)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                return UseEmailFallback(email);
            }

            var sanitized = name.Trim();

            // Remove invalid file system characters
            if (_settings.RemoveSpecialCharacters)
            {
                sanitized = RemoveInvalidCharacters(sanitized);
            }

            // Remove additional invalid characters specified in settings
            if (_settings.AdditionalInvalidCharacters?.Length > 0)
            {
                foreach (var invalidChar in _settings.AdditionalInvalidCharacters)
                {
                    sanitized = sanitized.Replace(invalidChar, "");
                }
            }

            // Replace spaces
            if (!string.IsNullOrEmpty(_settings.ReplaceSpacesWith))
            {
                sanitized = sanitized.Replace(" ", _settings.ReplaceSpacesWith);
            }

            // Handle Unicode
            if (!_settings.PreserveUnicode)
            {
                sanitized = ConvertToAscii(sanitized);
            }

            // Remove multiple consecutive separators
            sanitized = RemoveConsecutiveSeparators(sanitized);

            // Truncate if too long
            if (sanitized.Length > _settings.MaxLength)
            {
                sanitized = sanitized.Substring(0, _settings.MaxLength);
            }

            // Ensure the result is not empty
            if (string.IsNullOrWhiteSpace(sanitized))
            {
                return UseEmailFallback(email);
            }

            // Remove trailing dots and spaces (Windows doesn't like these)
            sanitized = sanitized.TrimEnd('.', ' ');

            // Ensure the result is still not empty after trimming
            if (string.IsNullOrWhiteSpace(sanitized))
            {
                return UseEmailFallback(email);
            }

            return sanitized;
        }

        /// <summary>
        /// Removes invalid file system characters from the input string
        /// </summary>
        private string RemoveInvalidCharacters(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var result = new StringBuilder();
            foreach (char c in input)
            {
                if (!InvalidFileNameChars.Contains(c) && !InvalidPathChars.Contains(c))
                {
                    result.Append(c);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Converts Unicode characters to ASCII equivalents
        /// </summary>
        private string ConvertToAscii(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // Normalize the string to decompose accented characters
            var normalizedString = input.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (char c in normalizedString)
            {
                var unicodeCategory = System.Globalization.CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != System.Globalization.UnicodeCategory.NonSpacingMark)
                {
                    // Only keep ASCII characters
                    if (c <= 127)
                    {
                        stringBuilder.Append(c);
                    }
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        /// <summary>
        /// Removes consecutive separator characters
        /// </summary>
        private string RemoveConsecutiveSeparators(string input)
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(_settings.ReplaceSpacesWith))
                return input;

            var separator = _settings.ReplaceSpacesWith;
            var pattern = Regex.Escape(separator) + "{2,}";
            return Regex.Replace(input, pattern, separator);
        }

        /// <summary>
        /// Uses email address as fallback when name is empty or invalid
        /// </summary>
        private string UseEmailFallback(string email)
        {
            if (string.IsNullOrWhiteSpace(email) || !_settings.UseEmailAsFallback)
            {
                return _settings.FallbackName;
            }

            try
            {
                // Extract the local part of the email (before @)
                var atIndex = email.IndexOf('@');
                if (atIndex > 0)
                {
                    var localPart = email.Substring(0, atIndex);
                    // Recursively sanitize the local part
                    var sanitizedLocal = SanitizeName(localPart);
                    
                    if (!string.IsNullOrWhiteSpace(sanitizedLocal))
                    {
                        return sanitizedLocal;
                    }
                }

                // If we can't extract a good local part, use the domain
                if (atIndex >= 0 && atIndex < email.Length - 1)
                {
                    var domain = email.Substring(atIndex + 1);
                    return $"{_settings.FallbackName}@{SanitizeName(domain)}";
                }
            }
            catch
            {
                // If anything goes wrong, fall back to the default
            }

            return _settings.FallbackName;
        }

        /// <summary>
        /// Validates that a sanitized name is safe for file system use
        /// </summary>
        public bool IsValidFileName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            // Check for invalid characters
            if (name.Any(c => InvalidFileNameChars.Contains(c) || InvalidPathChars.Contains(c)))
                return false;

            // Check for reserved names on Windows
            var reservedNames = new[] { "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9" };
            if (reservedNames.Contains(name.ToUpperInvariant()))
                return false;

            // Check for names ending with dots or spaces
            if (name.EndsWith(".") || name.EndsWith(" "))
                return false;

            return true;
        }
    }
}
