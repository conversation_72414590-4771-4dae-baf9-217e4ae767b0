﻿// <auto-generated />
using System;
using EmailProcessor.Infrastructure.Database.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EmailProcessor.Infrastructure.Migrations
{
    [DbContext(typeof(EmailProcessorContext))]
    [Migration("20250801092635_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Attachment", b =>
                {
                    b.Property<long>("AttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("AttachmentId"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DirectoryPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("EmailId")
                        .HasColumnType("bigint");

                    b.Property<long?>("EmailId1")
                        .HasColumnType("bigint");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("LocalErrorMessage")
                        .HasMaxLength(-1)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LocalProcessingStatus")
                        .HasColumnType("int");

                    b.Property<DateTime>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UncErrorMessage")
                        .HasMaxLength(-1)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UncProcessingStatus")
                        .HasColumnType("int");

                    b.HasKey("AttachmentId");

                    b.HasIndex("DirectoryPath")
                        .HasDatabaseName("IX_Attachments_DirectoryPath");

                    b.HasIndex("EmailId")
                        .HasDatabaseName("IX_Attachments_EmailId");

                    b.HasIndex("EmailId1");

                    b.HasIndex("FileExtension")
                        .HasDatabaseName("IX_Attachments_FileExtension");

                    b.HasIndex("LocalProcessingStatus")
                        .HasDatabaseName("IX_Attachments_LocalProcessingStatus");

                    b.HasIndex("UncProcessingStatus")
                        .HasDatabaseName("IX_Attachments_UncProcessingStatus");

                    b.ToTable("Attachments", (string)null);
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Configuration", b =>
                {
                    b.Property<int>("ConfigId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ConfigId"));

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ConfigValue")
                        .IsRequired()
                        .HasMaxLength(-1)
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ConfigId");

                    b.HasIndex("ConfigKey")
                        .IsUnique()
                        .HasDatabaseName("IX_Configuration_ConfigKey");

                    b.ToTable("Configuration", (string)null);
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Email", b =>
                {
                    b.Property<long>("EmailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("EmailId"));

                    b.PrimitiveCollection<string>("AllRecipients")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AllRecipientsJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EmailType")
                        .HasColumnType("int");

                    b.Property<DateTime>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OutlookMessageId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PrimaryRecipientEmail")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PrimaryRecipientName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ProcessingStatus")
                        .HasColumnType("int");

                    b.Property<string>("SenderName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("EmailId");

                    b.HasIndex("EmailType")
                        .HasDatabaseName("IX_Emails_EmailType");

                    b.HasIndex("OutlookMessageId")
                        .IsUnique()
                        .HasDatabaseName("IX_Emails_OutlookMessageId");

                    b.HasIndex("PrimaryRecipientName")
                        .HasDatabaseName("IX_Emails_PrimaryRecipientName");

                    b.HasIndex("ProcessingStatus")
                        .HasDatabaseName("IX_Emails_ProcessingStatus");

                    b.HasIndex("SenderName")
                        .HasDatabaseName("IX_Emails_SenderName");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_Emails_Timestamp");

                    b.ToTable("Emails", (string)null);
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.ProcessingLog", b =>
                {
                    b.Property<long>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("LogId"));

                    b.Property<long?>("AttachmentId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AttachmentId1")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("EmailId")
                        .HasColumnType("bigint");

                    b.Property<string>("ExceptionDetails")
                        .HasMaxLength(-1)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LogLevel")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(-1)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SourceComponent")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("LogId");

                    b.HasIndex("AttachmentId")
                        .HasDatabaseName("IX_ProcessingLogs_AttachmentId");

                    b.HasIndex("AttachmentId1");

                    b.HasIndex("CorrelationId")
                        .HasDatabaseName("IX_ProcessingLogs_CorrelationId");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("IX_ProcessingLogs_CreatedDate");

                    b.HasIndex("EmailId")
                        .HasDatabaseName("IX_ProcessingLogs_EmailId");

                    b.HasIndex("LogLevel")
                        .HasDatabaseName("IX_ProcessingLogs_LogLevel");

                    b.ToTable("ProcessingLogs", (string)null);
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Attachment", b =>
                {
                    b.HasOne("EmailProcessor.Domain.Entities.Email", null)
                        .WithMany("Attachments")
                        .HasForeignKey("EmailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmailProcessor.Domain.Entities.Email", "Email")
                        .WithMany()
                        .HasForeignKey("EmailId1");

                    b.OwnsOne("EmailProcessor.Domain.ValueObjects.FilePath", "LocalStoragePath", b1 =>
                        {
                            b1.Property<long>("AttachmentId")
                                .HasColumnType("bigint");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("nvarchar(1000)")
                                .HasColumnName("LocalStoragePath");

                            b1.HasKey("AttachmentId");

                            b1.ToTable("Attachments");

                            b1.WithOwner()
                                .HasForeignKey("AttachmentId");
                        });

                    b.OwnsOne("EmailProcessor.Domain.ValueObjects.FilePath", "UncStoragePath", b1 =>
                        {
                            b1.Property<long>("AttachmentId")
                                .HasColumnType("bigint");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("nvarchar(1000)")
                                .HasColumnName("UncStoragePath");

                            b1.HasKey("AttachmentId");

                            b1.ToTable("Attachments");

                            b1.WithOwner()
                                .HasForeignKey("AttachmentId");
                        });

                    b.Navigation("Email");

                    b.Navigation("LocalStoragePath");

                    b.Navigation("UncStoragePath");
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Email", b =>
                {
                    b.OwnsMany("EmailProcessor.Domain.ValueObjects.EmailAddress", "RecipientCC", b1 =>
                        {
                            b1.Property<long>("EmailId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            SqlServerPropertyBuilderExtensions.UseIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<string>("Value")
                                .HasMaxLength(2000)
                                .HasColumnType("nvarchar(2000)")
                                .HasColumnName("RecipientCC");

                            b1.HasKey("EmailId", "Id");

                            b1.ToTable("Emails_RecipientCC");

                            b1.WithOwner()
                                .HasForeignKey("EmailId");
                        });

                    b.OwnsMany("EmailProcessor.Domain.ValueObjects.EmailAddress", "RecipientTo", b1 =>
                        {
                            b1.Property<long>("EmailId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            SqlServerPropertyBuilderExtensions.UseIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(2000)
                                .HasColumnType("nvarchar(2000)")
                                .HasColumnName("RecipientTo");

                            b1.HasKey("EmailId", "Id");

                            b1.ToTable("Emails_RecipientTo");

                            b1.WithOwner()
                                .HasForeignKey("EmailId");
                        });

                    b.OwnsOne("EmailProcessor.Domain.ValueObjects.EmailAddress", "SenderEmail", b1 =>
                        {
                            b1.Property<long>("EmailId")
                                .HasColumnType("bigint");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("nvarchar(200)")
                                .HasColumnName("SenderEmail");

                            b1.HasKey("EmailId");

                            b1.ToTable("Emails");

                            b1.WithOwner()
                                .HasForeignKey("EmailId");
                        });

                    b.Navigation("RecipientCC");

                    b.Navigation("RecipientTo");

                    b.Navigation("SenderEmail");
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.ProcessingLog", b =>
                {
                    b.HasOne("EmailProcessor.Domain.Entities.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("EmailProcessor.Domain.Entities.Attachment", null)
                        .WithMany("ProcessingLogs")
                        .HasForeignKey("AttachmentId1");

                    b.HasOne("EmailProcessor.Domain.Entities.Email", "Email")
                        .WithMany("ProcessingLogs")
                        .HasForeignKey("EmailId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Attachment");

                    b.Navigation("Email");
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Attachment", b =>
                {
                    b.Navigation("ProcessingLogs");
                });

            modelBuilder.Entity("EmailProcessor.Domain.Entities.Email", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("ProcessingLogs");
                });
#pragma warning restore 612, 618
        }
    }
}
