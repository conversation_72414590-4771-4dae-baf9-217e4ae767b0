using System;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using Xunit;

namespace EmailProcessor.Infrastructure.Tests.Storage
{
    public class UncStorageProviderTests
    {
        private readonly ILoggingProvider _loggingProvider;

        public UncStorageProviderTests()
        {
            _loggingProvider = new TestLoggingProvider();
        }

        [Fact]
        public void Constructor_WithInvalidUncPath_ThrowsArgumentException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                new UncStorageProvider("invalid-path", _loggingProvider));
            
            Assert.Contains("UncPath must be a valid UNC path", exception.Message);
        }

        [Fact]
        public void Constructor_WithNullUncPath_ThrowsArgumentException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                new UncStorageProvider(null, _loggingProvider));
            
            Assert.Contains("UncPath cannot be null or empty", exception.Message);
        }

        [Fact]
        public void Constructor_WithEmptyUncPath_ThrowsArgumentException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                new UncStorageProvider("", _loggingProvider));
            
            Assert.Contains("UncPath cannot be null or empty", exception.Message);
        }

        [Fact]
        public void Constructor_WithWhitespaceUncPath_ThrowsArgumentException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                new UncStorageProvider("   ", _loggingProvider));
            
            Assert.Contains("UncPath cannot be null or empty", exception.Message);
        }

        [Fact]
        public void NullStorageProvider_ImplementsIStorageProvider()
        {
            // Arrange
            var nullProvider = new NullStorageProvider(_loggingProvider);

            // Assert
            Assert.Equal(EmailProcessor.Domain.Interfaces.StorageType.Unc, nullProvider.StorageType);
            Assert.Equal(string.Empty, nullProvider.BasePath);
        }

        [Fact]
        public async Task NullStorageProvider_SaveFileAsync_ThrowsInvalidOperationException()
        {
            // Arrange
            var nullProvider = new NullStorageProvider(_loggingProvider);
            var fileData = new byte[] { 1, 2, 3 };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                nullProvider.SaveFileAsync(fileData, "test.txt", "test-dir"));
            
            Assert.Contains("UNC storage is not available", exception.Message);
        }

        [Fact]
        public async Task NullStorageProvider_TestConnectivityAsync_ReturnsFalse()
        {
            // Arrange
            var nullProvider = new NullStorageProvider(_loggingProvider);

            // Act
            var result = await nullProvider.TestConnectivityAsync();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task NullStorageProvider_GetAvailableSpaceAsync_ReturnsZero()
        {
            // Arrange
            var nullProvider = new NullStorageProvider(_loggingProvider);

            // Act
            var result = await nullProvider.GetAvailableSpaceAsync();

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public async Task NullStorageProvider_FileExistsAsync_ReturnsFalse()
        {
            // Arrange
            var nullProvider = new NullStorageProvider(_loggingProvider);

            // Act
            var result = await nullProvider.FileExistsAsync("test.txt");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task NullStorageProvider_DirectoryExistsAsync_ReturnsFalse()
        {
            // Arrange
            var nullProvider = new NullStorageProvider(_loggingProvider);

            // Act
            var result = await nullProvider.DirectoryExistsAsync("test-dir");

            // Assert
            Assert.False(result);
        }

        private class TestLoggingProvider : ILoggingProvider
        {
            public LogLevel CurrentLogLevel => LogLevel.Information;

            public Task LogAsync(LogLevel logLevel, string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogDebugAsync(string message, string sourceComponent, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogErrorAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogFatalAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogInformationAsync(string message, string sourceComponent, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public Task LogWarningAsync(string message, string sourceComponent, Guid? correlationId = null)
            {
                return Task.CompletedTask;
            }

            public void SetLogLevel(LogLevel logLevel) { }

            public bool IsEnabled(LogLevel logLevel) => true;
        }
    }
} 