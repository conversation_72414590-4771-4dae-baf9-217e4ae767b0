﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="EmailProcessor.AddIn.vsto" version="*******" publicKeyToken="fceb4e9b9fc36b7d" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="EmailProcessor.AddIn" asmv2:product="EmailProcessor.AddIn" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="EmailProcessor.AddIn.dll.manifest" size="13943">
      <assemblyIdentity name="EmailProcessor.AddIn.dll" version="*******" publicKeyToken="fceb4e9b9fc36b7d" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>JGnWcw7m/Naa1C7phRcFDu44EszBL59l1WAJLtS9H+s=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=RAINMAKER\alifa" issuerKeyHash="1f2e9ca8478379bf8748c9ecc69d9ba573e89f80" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>dSikILkbg1Ep8I05ScABWQ8aAuU4ejW4xBmiI/Tkpxw=</DigestValue></Reference></SignedInfo><SignatureValue>xskOoI8BrC+pHIaar5LHBph+XdXUF3iqEyE5H7d27effKmahdbDotXAGgPpfKe8iFuMYMhlpLE3qe747Tvq8m2Vfrlj8lofvJFQCHf8rgMkBIlZ1oz0BY6MlMLuFuLVFJKaCfnWyaT+31AN9dy5Xkcn1BlWo2LPaH65xWw/wRnE=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>37Kz6WFcO4AK+/FAr8Cx5YG6XSOD2nXI62qE/ckAkWz3IXxsSeRrrnMOupYFsIQagzIUsMsK2rf98rQ8Hz0KjkNHSqsFUmoBOhvA2PqHIAEwBnni629bXxMHSwXhFZaqnlgFNBw3cwoKwo4wcweW/zE2GkWkWKJC7FYanhhAnkk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="1ca7e4f423a219c4b8357a38e5021a0f5901c049398df02951831bb920a42875" Description="" Url=""><as:assemblyIdentity name="EmailProcessor.AddIn.vsto" version="*******" publicKeyToken="fceb4e9b9fc36b7d" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=RAINMAKER\alifa</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>/I/xQpITzLTlhRErJSZKdtaFd0TZFRm4PlPlkZMNz+A=</DigestValue></Reference></SignedInfo><SignatureValue>Ti6TqaAavd5wWNZQ6ni1KKPM/+CMFC/MbfdxzXtpLx9Bp168DIV/zN8J1mEQrT/IUTWIaGdGabFuYG92sgFq6oULCsdiN5Lx1419CYMGjenMek9uELEmCf59bxgOp41zm1Epdk4c6fPqXMhmsCBHlSVZxsdMU8fCmdNAp8RbvmQ=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>37Kz6WFcO4AK+/FAr8Cx5YG6XSOD2nXI62qE/ckAkWz3IXxsSeRrrnMOupYFsIQagzIUsMsK2rf98rQ8Hz0KjkNHSqsFUmoBOhvA2PqHIAEwBnni629bXxMHSwXhFZaqnlgFNBw3cwoKwo4wcweW/zE2GkWkWKJC7FYanhhAnkk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB1TCCAT6gAwIBAgIQHyHmdEZ7sqZPBR34/z+wsTANBgkqhkiG9w0BAQsFADApMScwJQYDVQQDHh4AUgBBAEkATgBNAEEASwBFAFIAXABhAGwAaQBmAGEwHhcNMjUwNzMxMjM1NjU4WhcNMjYwODAxMDU1NjU4WjApMScwJQYDVQQDHh4AUgBBAEkATgBNAEEASwBFAFIAXABhAGwAaQBmAGEwgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAN+ys+lhXDuACvvxQK/AseWBul0jg9p1yOtqhP3JAJFs9yF8bEnka65zDrqWBbCEGoMyFLDLCtq3/fK0PB89Co5DR0qrBVJqATobwNj6hyABMAZ54utvW18TB0sF4RWWqp5YBTQcN3MKCsKOMHMHlv8xNhpFpFiiQuxWGp4YQJ5JAgMBAAEwDQYJKoZIhvcNAQELBQADgYEAi8AHoHJHTfvSbE1B9YucllhEKTekNbVrMOe8hlsIJFyrCwLSJpinGjskhyA+AjuL0Xqs68HAMsyzl0UfptzPh7mZvE5uIythTeOEji6KmywObCjAgb09dgUixZS0YBRSd/vWywnPy7KdLQMbeLhdAw/NZYzSmw0wOmiDWUGUFsU=</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>