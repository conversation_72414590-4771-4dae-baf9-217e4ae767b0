﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="EmailProcessor.AddIn.vsto" version="*******" publicKeyToken="fceb4e9b9fc36b7d" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="EmailProcessor.AddIn" asmv2:product="EmailProcessor.AddIn" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="EmailProcessor.AddIn.dll.manifest" size="22360">
      <assemblyIdentity name="EmailProcessor.AddIn.dll" version="*******" publicKeyToken="fceb4e9b9fc36b7d" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>sbd8ZoTMEEKGDHKJwe211kS+1KayXEGdVjX3mix/HCA=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=RAINMAKER\alifa" issuerKeyHash="1f2e9ca8478379bf8748c9ecc69d9ba573e89f80" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>o08GZBkW32I9W8rfxH/3BamgqoHcqzzmVmK/Mj/VGPA=</DigestValue></Reference></SignedInfo><SignatureValue>ulC3JNHUGcXePZxIsq5eS8RAQleZGe+KjQ7pF46+THPBYnqBzsnpyn/L7tN2xoG19JVtn9bM6Eesje+jT2b+D/oKFhalA4Yqdx+9fMGzJ8gUtpt6bIlZLuNg4Gk+xagx7mqQBjsgTZbqV5pQIjI+j2um/yA6tOdAeGhsui0O3WQ=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>37Kz6WFcO4AK+/FAr8Cx5YG6XSOD2nXI62qE/ckAkWz3IXxsSeRrrnMOupYFsIQagzIUsMsK2rf98rQ8Hz0KjkNHSqsFUmoBOhvA2PqHIAEwBnni629bXxMHSwXhFZaqnlgFNBw3cwoKwo4wcweW/zE2GkWkWKJC7FYanhhAnkk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="f018d53f32bf6256e63cabdc81aaa0a905f77fc4dfca5b3d62df161964064fa3" Description="" Url=""><as:assemblyIdentity name="EmailProcessor.AddIn.vsto" version="*******" publicKeyToken="fceb4e9b9fc36b7d" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=RAINMAKER\alifa</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>M9mBwS8EAXjOB5gldFPfvB1yvvF91TbDrYtITJgOcN4=</DigestValue></Reference></SignedInfo><SignatureValue>3qpfAAzy8JVVbSSYVp+8sKoSj9Gv5X0hpx9N8AZ0ZZlvZtBDUgCv7G1zlYfMaab701xz2DI2E08d5zQn7f+SomxNrcFLBXihmiJoe+wuDxvyqK0NNngvvflpHDxiF/oPnTtD/WYMUQUvgqE1AmxNizNZfxwq72yVK5P/SualGsg=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>37Kz6WFcO4AK+/FAr8Cx5YG6XSOD2nXI62qE/ckAkWz3IXxsSeRrrnMOupYFsIQagzIUsMsK2rf98rQ8Hz0KjkNHSqsFUmoBOhvA2PqHIAEwBnni629bXxMHSwXhFZaqnlgFNBw3cwoKwo4wcweW/zE2GkWkWKJC7FYanhhAnkk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB1TCCAT6gAwIBAgIQHyHmdEZ7sqZPBR34/z+wsTANBgkqhkiG9w0BAQsFADApMScwJQYDVQQDHh4AUgBBAEkATgBNAEEASwBFAFIAXABhAGwAaQBmAGEwHhcNMjUwNzMxMjM1NjU4WhcNMjYwODAxMDU1NjU4WjApMScwJQYDVQQDHh4AUgBBAEkATgBNAEEASwBFAFIAXABhAGwAaQBmAGEwgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAN+ys+lhXDuACvvxQK/AseWBul0jg9p1yOtqhP3JAJFs9yF8bEnka65zDrqWBbCEGoMyFLDLCtq3/fK0PB89Co5DR0qrBVJqATobwNj6hyABMAZ54utvW18TB0sF4RWWqp5YBTQcN3MKCsKOMHMHlv8xNhpFpFiiQuxWGp4YQJ5JAgMBAAEwDQYJKoZIhvcNAQELBQADgYEAi8AHoHJHTfvSbE1B9YucllhEKTekNbVrMOe8hlsIJFyrCwLSJpinGjskhyA+AjuL0Xqs68HAMsyzl0UfptzPh7mZvE5uIythTeOEji6KmywObCjAgb09dgUixZS0YBRSd/vWywnPy7KdLQMbeLhdAw/NZYzSmw0wOmiDWUGUFsU=</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>