using System;
using System.IO.Pipes;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;

namespace EmailProcessor.AddIn.Communication
{
    /// <summary>
    /// Named Pipe client for communication with Windows Service
    /// </summary>
    public class NamedPipeClient : INamedPipeClient
    {
        private readonly string _pipeName;
        private NamedPipeClientStream _pipeClient;
        private bool _disposed;
        private readonly object _lockObject = new object();

        public bool IsConnected => _pipeClient != null && _pipeClient.IsConnected;

        public event EventHandler ConnectionLost;
        public event EventHandler Connected;

        public NamedPipeClient(string pipeName)
        {
            _pipeName = pipeName ?? throw new ArgumentNullException(nameof(pipeName));
        }

        public async Task<bool> ConnectAsync(int timeoutSeconds = 10)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_pipeClient != null && _pipeClient.IsConnected)
                    {
                        return true; // Already connected
                    }

                    // Dispose existing connection if any
                    if (_pipeClient != null)
                    {
                        _pipeClient.Dispose();
                    }

                    // Create new pipe client
                    _pipeClient = new NamedPipeClientStream(
                        ".", // Server name (local machine)
                        _pipeName,
                        PipeDirection.InOut,
                        PipeOptions.Asynchronous);
                }

                // Connect with timeout
                var connectTask = _pipeClient.ConnectAsync();
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(timeoutSeconds));

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                if (completedTask == timeoutTask)
                {
                    // Timeout occurred
                    if (_pipeClient != null)
                    {
                        _pipeClient.Dispose();
                        _pipeClient = null;
                    }
                    return false;
                }

                // Wait for connection to complete
                await connectTask;

                if (_pipeClient != null && _pipeClient.IsConnected)
                {
                    if (Connected != null)
                    {
                        Connected.Invoke(this, EventArgs.Empty);
                    }
                    return true;
                }

                return false;
            }
            catch (Exception)
            {
                if (_pipeClient != null)
                {
                    _pipeClient.Dispose();
                    _pipeClient = null;
                }
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    if (_pipeClient != null)
                    {
                        _pipeClient.Close();
                        _pipeClient.Dispose();
                        _pipeClient = null;
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception)
            {
                // Ignore errors during disconnect
            }
        }

        public async Task<ProcessingResponse> SendMessageAsync(ProcessingMessage message)
        {
            if (_pipeClient == null || !_pipeClient.IsConnected)
            {
                throw new InvalidOperationException("Not connected to Named Pipe server");
            }

            try
            {
                // Serialize message to JSON
                var messageJson = JsonSerializer.Serialize(message, new JsonSerializerOptions
                {
                    WriteIndented = false
                });

                var messageBytes = Encoding.UTF8.GetBytes(messageJson);

                // Send message
                await _pipeClient.WriteAsync(messageBytes, 0, messageBytes.Length);

                // Read response
                var response = await ReadResponseAsync();

                return response;
            }
            catch (Exception ex)
            {
                // Check if connection was lost
                if (_pipeClient == null || !_pipeClient.IsConnected)
                {
                    if (ConnectionLost != null)
                    {
                        ConnectionLost.Invoke(this, EventArgs.Empty);
                    }
                }

                throw new InvalidOperationException($"Failed to send message: {ex.Message}", ex);
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (_pipeClient == null || !_pipeClient.IsConnected)
                {
                    return await ConnectAsync(5); // Quick connection test
                }

                // Send a simple test message
                var testMessage = new ProcessingMessage
                {
                    MessageType = "TestConnection",
                    CorrelationId = Guid.NewGuid(),
                    Timestamp = DateTime.UtcNow
                };

                var response = await SendMessageAsync(testMessage);
                return response != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private async Task<ProcessingResponse> ReadResponseAsync()
        {
            if (_pipeClient == null || !_pipeClient.IsConnected)
            {
                throw new InvalidOperationException("Not connected to Named Pipe server");
            }

            var buffer = new byte[4096];
            var messageBuilder = new StringBuilder();

            while (true)
            {
                var bytesRead = await _pipeClient.ReadAsync(buffer, 0, buffer.Length);
                
                if (bytesRead == 0)
                {
                    // Server disconnected
                    if (ConnectionLost != null)
                    {
                        ConnectionLost.Invoke(this, EventArgs.Empty);
                    }
                    throw new InvalidOperationException("Server disconnected");
                }

                var messageChunk = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                messageBuilder.Append(messageChunk);

                // Check if we have a complete JSON message
                var message = messageBuilder.ToString();
                if (IsCompleteMessage(message))
                {
                    try
                    {
                        var response = JsonSerializer.Deserialize<ProcessingResponse>(message, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        return response != null ? response : new ProcessingResponse
                        {
                            Success = false,
                            Message = "Failed to deserialize response"
                        };
                    }
                    catch (JsonException ex)
                    {
                        throw new InvalidOperationException($"Failed to deserialize response: {ex.Message}");
                    }
                }
            }
        }

        private static bool IsCompleteMessage(string message)
        {
            // Simple check for complete JSON message
            return message.Trim().StartsWith("{") && message.Trim().EndsWith("}");
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                if (_pipeClient != null)
                {
                    _pipeClient.Dispose();
                }
                _disposed = true;
            }
        }
    }
} 