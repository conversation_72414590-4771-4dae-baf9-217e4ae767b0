using System;
using System.Collections.Generic;

namespace EmailProcessor.AddIn.Interfaces
{
    /// <summary>
    /// Interface for VSTO configuration management
    /// </summary>
    public interface IVSTOConfiguration
    {
        /// <summary>
        /// Gets the Named Pipe server name
        /// </summary>
        string NamedPipeName { get; }

        /// <summary>
        /// Gets the connection timeout in seconds
        /// </summary>
        int ConnectionTimeoutSeconds { get; }

        /// <summary>
        /// Gets the maximum file size to process in bytes
        /// </summary>
        long MaxFileSizeBytes { get; }

        /// <summary>
        /// Gets the retry count for failed operations
        /// </summary>
        int RetryCount { get; }

        /// <summary>
        /// Gets the retry delay in seconds
        /// </summary>
        int RetryDelaySeconds { get; }

        /// <summary>
        /// Gets whether to process received emails
        /// </summary>
        bool ProcessReceivedEmails { get; }

        /// <summary>
        /// Gets whether to process sent emails
        /// </summary>
        bool ProcessSentEmails { get; }

        /// <summary>
        /// Gets the list of excluded file extensions
        /// </summary>
        List<string> ExcludedFileExtensions { get; }

        /// <summary>
        /// Gets the log file path
        /// </summary>
        string LogFilePath { get; }

        /// <summary>
        /// Gets the log level
        /// </summary>
        string LogLevel { get; }

        /// <summary>
        /// Loads configuration from registry
        /// </summary>
        void LoadConfiguration();

        /// <summary>
        /// Saves configuration to registry
        /// </summary>
        void SaveConfiguration();

        /// <summary>
        /// Gets a configuration value by key
        /// </summary>
        /// <param name="key">Configuration key</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>Configuration value</returns>
        string GetValue(string key, string defaultValue = "");

        /// <summary>
        /// Sets a configuration value
        /// </summary>
        /// <param name="key">Configuration key</param>
        /// <param name="value">Configuration value</param>
        void SetValue(string key, string value);
    }
} 